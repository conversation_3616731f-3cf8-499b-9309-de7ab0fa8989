/**package com.magnamedia.scheduledjobs;

import com.magnamedia.controller.ContractController;
import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.Payment;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.QueryService;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;


 * <AUTHOR>
 *         Created on 11, 4, 2020
 *         Jirra ACC-1597



public class DirectDebitClientDidNotSignJob implements MagnamediaJob {


    @Override
    public void run(Map<String, ?> map) {
        scheduleForTermination();
    }


    public void scheduleForTermination() {

        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);

        SelectQuery<ContractPaymentTerm> query = new SelectQuery(getQuery(), "", ContractPaymentTerm.class, new HashMap());
        List<ContractPaymentTerm> contractPaymentTerms = query.execute();

        ContractController contractController = Setup.getApplicationContext().getBean(ContractController.class);

        for (ContractPaymentTerm contractPaymentTerm : contractPaymentTerms) {
            Contract contract = contractPaymentTerm.getContract();

            if (contract == null) {
                logger.log(Level.SEVERE, "DirectDebitClientDidNotSignJob scheduleForTermination contract is null");
                continue;
            }

            logger.log(Level.SEVERE, "DirectDebitClientDidNotSignJob scheduleForTermination contract: " + contract.getId());

            List<Payment> cashReceivedPayment = paymentRepository.findByContractAndMethodOfPaymentNotInAndStatus(contractPaymentTerm.getContract(), Arrays.asList(PaymentMethod.DIRECT_DEBIT), PaymentStatus.RECEIVED);
            if (cashReceivedPayment == null || cashReceivedPayment.isEmpty()) {
                logger.log(Level.SEVERE, "DirectDebitClientDidNotSignJob scheduleForTermination has no direct debit received payment");
                continue;
            }

            if (!directDebitRepository.existsAtLeastOneDDByStatusEqualAndContract(DirectDebitStatus.IN_COMPLETE, contract)) {
                logger.log(Level.SEVERE, "DirectDebitClientDidNotSignJob contract has an no incomplete dd so we dont run the job");
                continue;
            }

            LocalDate paidEndDate = new LocalDate(contract.getPaidEndDate());
            LocalDate today = new LocalDate();

            logger.log(Level.SEVERE, "DirectDebitClientDidNotSignJob scheduleForTermination paidEndDate: " + paidEndDate);
            logger.log(Level.SEVERE, "DirectDebitClientDidNotSignJob scheduleForTermination today: " + today);

            contractController.setContractForTermination(contract,
                    paidEndDate.isAfter(today) ? paidEndDate.toDate() : today.toDate(),
                    "client_did_not_sign_dd_after_x_days",
                    "CANCELLATION"
            );
        }

    }

    private String getQuery() {

        String query = Setup.getApplicationContext().getBean(QueryService.class).getContractToBeSignedQuery(true,
                " dd.source " + " != "
                        + "'" + DirectDebitSource.PAYMENT_EXPIRY_AUTO_EXTENSION + "' " + " or dd.source IS NULL",
                Arrays.asList(ContractStatus.ACTIVE, ContractStatus.PLANNED_RENEWAL));

        String days = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_SCHEDULE_FOR_TERMINATION_AFTER_DAYS);

        query += " and con.scheduledDateOfTermination IS NULL and ";

        DateTime now = DateTime.now();
        DateTime endOfLastMonth = new DateTime(DateUtil.getDayEnd(DateTime.now().minusMonths(1).dayOfMonth().withMaximumValue().toDate()));
        DateTime startOfLastMonth = DateTime.now().dayOfMonth().withMinimumValue().minusMonths(1).withTimeAtStartOfDay();

        query += "con.startOfContract between '" + formatDate(startOfLastMonth) + "' and '" + formatDate(endOfLastMonth) + "' and " +
                now.getDayOfMonth() + " = " + Integer.parseInt(days);

        return query;
    }

    private String formatDate(DateTime dateTime) {
        return dateTime.year().get() + "-" + dateTime.monthOfYear().get() + "-" + dateTime.getDayOfMonth() + " " +
                dateTime.getHourOfDay() + ":" + dateTime.getMinuteOfHour() + ":" + dateTime.getSecondOfMinute();
    }
}
*/