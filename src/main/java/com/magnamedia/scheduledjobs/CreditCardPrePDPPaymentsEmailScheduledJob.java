package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.MailService;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Payment;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.ReportMail;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.report.TemplatedReport;
import com.magnamedia.repository.PaymentRepository;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.LocalDate;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR> <PERSON>
 * ACC-3856
 */
public class CreditCardPrePDPPaymentsEmailScheduledJob implements TemplatedReport<Payment>, MagnamediaJob {

    private MailService mailService;
    private TemplateEngine templateEngine;
    private ReportMail reportMail;
    private PaymentRepository repository;
    
    private static final Logger LOG = Logger.getLogger(CreditCardPrePDPPaymentsEmailScheduledJob.class.getName());

    public CreditCardPrePDPPaymentsEmailScheduledJob() {
        mailService = Setup.getMailService();
        templateEngine = Setup.getApplicationContext().getBean(TemplateEngine.class);
        reportMail = Setup.getApplicationContext().getBean(ReportMail.class);
        repository = Setup.getRepository(PaymentRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        LOG.info("CreditCardPrePDPPaymentsEmailScheduledJob Run ");
        this.SendEmail();
    }

    public String SendEmail() {
        StringBuilder tableBuilder = new StringBuilder("");

        try {
            LOG.info("CreditCardPrePDPPaymentsEmailScheduledJob after refresh data  ");
            tableBuilder.append(reportMail.toHtmlTableNoCount(getTable(), getHeaders(), getTitle(), 400));
            tableBuilder.append("<br/><hr/>");

        } catch (Throwable e) {
            tableBuilder.append("<p>").append(ExceptionUtils.getStackTrace(e)).append("</p>");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("title", getTitle());
        params.put("tableData", tableBuilder.toString());



        String emails = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PRE_PDP_PAYMENT_WITHIN_WEEK_EMAILS);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);

        if (recipients.size() > 0 && !getData().isEmpty()) {
            mailService.sendEmail(recipients,
                    new TemplateEmail("PRE-PDP online Credit Card Payment", "PDCsDueDateEmail", params), null);
        }
        return "";
    }

    @Override
    public List<Payment> getData() {
        return repository.findByMethodOfPaymentAndStatus(PaymentMethod.CARD, PaymentStatus.PRE_PDP);
    }

    @Override
    public String[] toStringArray(Payment obj) {
        LOG.info("CreditCardPrePDPPaymentsEmailScheduledJob obj " + obj);
        
        LocalDate date = LocalDate.fromDateFields(obj.getCreationDate());
        LocalDate dateOfPayment = LocalDate.fromDateFields(obj.getDateOfPayment());
        
        return new String[]{ obj.getContract().getClient().getName(), obj.getContract().getId().toString(),
                dateOfPayment.toString("dd/MM/yyyy"), date.toString("dd/MM/yyyy") };
    }

    @Override
    public String getTitle() {
        return "Kindly be advised that the below clients haven't completed their Credit card payments yet.";
    }

    @Override
    public String[] getHeaders() {
        return new String[]{ "Client Name", "Contract", "Payment Date", "Creation Date" };
    }
    
    @Override
    public void refreshData() {
    }
}
