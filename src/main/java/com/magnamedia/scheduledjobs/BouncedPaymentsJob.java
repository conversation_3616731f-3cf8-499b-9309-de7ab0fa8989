//package com.magnamedia.scheduledjobs;
//
//import com.fasterxml.jackson.databind.node.ObjectNode;
//import com.magnamedia.controller.PaymentController;
//import com.magnamedia.core.Setup;
//import com.magnamedia.core.configuration.ObjectMapperConfiguration;
//import com.magnamedia.core.entity.PicklistItem;
//import com.magnamedia.core.helper.SelectFilter;
//import com.magnamedia.core.helper.SelectQuery;
//import com.magnamedia.entity.ClientTransferDetails;
//import com.magnamedia.entity.Payment;
//import com.magnamedia.entity.sms.SmsExtendedLog;
//import com.magnamedia.helper.*;
//import com.magnamedia.module.AccountingModule;
//import com.magnamedia.module.SmsTemplatesSetup;
//import com.magnamedia.module.type.ContractStatus;
//import com.magnamedia.module.type.PaymentStatus;
//import com.magnamedia.repository.ClientTransferDetailsRepository;
//import com.magnamedia.repository.PaymentRepository;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.Calendar;
//import java.util.Date;
//import java.util.List;
//
///**
// * <AUTHOR> Esrawi <<EMAIL>>
// *         Created at Jun 21, 2019
// * <AUTHOR> kanaan <<EMAIL>>
// *         Jirra ACC-1092
// */
//@RestController
//@RequestMapping("/bouncedpaymentsmsgscheduledjob")
//public class BouncedPaymentsJob {
//
//    @Autowired
//    NewSmsHelper newSmsHelper;
//    @Autowired
//    ClientTransferDetailsRepository clientTransferDetailsRepository;
//
//    @RequestMapping(path = "/run1", method = RequestMethod.GET)
//    //must be scheduled daily on 10AM
//    //@com.magnamedia.core.annotation.MultiTenantsAdvised @Scheduled(cron = "0 0 10 * * ?")
//    public void runPack4ExceptBigchecks() {
//        if (!isJobEnabled()) {
//            return;
//        }
//        Yesterday(null);
//
//        DaysAgo2(null);
//
//        DaysAgo3(null);
//
//        DaysAgo4(null);
//
//        DaysAgo5(null);
//
//    }
//
//    @RequestMapping(path = "/run2", method = RequestMethod.GET)
//    //must be scheduled each 3rd of month on 10AM
//    //@com.magnamedia.core.annotation.MultiTenantsAdvised @Scheduled(cron = "0 0 10 3 1/1 *")
//    public void runPack4BigChecks() {
//        if (!isJobEnabled()) {
//            return;
//        }
//        List<Payment> bigchecks = getBigChecks();
//        sendMsg_4_8(bigchecks);
//    }
//
//    public void DaysAgo5(List<Payment> payments) {
//        //payments bounced 5days ago
//        List<Payment> _5daysAgo = getBouncedPayments(-5);
//        if (payments == null) {
//            sendMsg_4_6_1(_5daysAgo);
//            sendMsg_4_6_2(_5daysAgo);
//        } else {
//            sendMsg_4_6_1(payments);
//            sendMsg_4_6_2(payments);
//        }
//    }
//
//    public void DaysAgo4(List<Payment> payments) {
//        //payments bounced 4days ago
//        List<Payment> _4daysAgo = getBouncedPayments(-4);
//        if (payments == null)
//            sendMsg_4_5(_4daysAgo);
//        else
//            sendMsg_4_5(payments);
//    }
//
//    public void DaysAgo3(List<Payment> payments) {
//        //payments bounced 3days ago
//        List<Payment> _3daysAgo = getBouncedPayments(-3);
//        if (payments == null)
//            sendMsg_4_4(_3daysAgo);
//        else
//            sendMsg_4_4(payments);
//    }
//
//    public void DaysAgo2(List<Payment> payments) {
//        //payments bounced 2days ago
//        List<Payment> _2daysAgo = getBouncedPayments(-2);
//        if (payments == null)
//            sendMsg_4_3(_2daysAgo);
//        else
//            sendMsg_4_3(payments);
//    }
//
//    public void Yesterday(List<Payment> payments) {
//        //payments bounced yesterday
//        List<Payment> _yesterday = getBouncedPayments(-1);
//        if (payments == null)
//            sendMsg_4_2(_yesterday);
//        else
//            sendMsg_4_2(payments);
//    }
//
//    public void Today(List<Payment> payments) {
//        PaymentController paymentController = Setup.getApplicationContext().getBean(PaymentController.class);
//        //payments bounced yesterday
//        //sendMsg_4_1(payments);
//        for (Payment payment : payments) {
////            payment.setIncludedInVatFix(!payment.getIncludedInVatFix());
//            ObjectNode jsonNode = new HibernateAwareObjectMapper().createObjectNode();
//            jsonNode.put("id", payment.getId());
//            jsonNode.put("includedInVatFix", !payment.getIncludedInVatFix());
//            paymentController.postPaymentAsJsonNodeToClientModule(jsonNode);
//        }
//    }
//
//
//    public void sendBouncedPaymentMsg(Payment p) {
//        sendMsg_4_1(p);
//    }
//
//    public void sendreplacedPaymentMsg(Payment p) {
//        sendMsg_4_7(p);
//    }
//
//    private List<Payment> getBouncedPayments(int i) {
//        Date d = DateUtil.addDays(new Date(), i);
//
//        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
//        query.filterBy("status", "=", PaymentStatus.BOUNCED);
//        query.filterBy("contract.status", "=", ContractStatus.ACTIVE);
//        query.filterBy("dateOfBouncing", ">=", DateUtil.getDayStart(d));
//        query.filterBy("dateOfBouncing", "<=", DateUtil.getDayEnd(d));
//        query.filterBy("replaced", "=", false);
//
//        return query.execute();
//    }
//
//    private void sendMsg_4_1(Payment p) {
//        ClientTransferDetails ctd = new ClientTransferDetails();
//        ctd.setClient(p.getContract().getClient());
//        ctd.setContract(p.getContract());
//        ctd.setPayment(p);
//        ctd.setAmount(p.getAmountOfPayment());
//        ctd.setFirstBouncedMsg(true);
//        ctd = clientTransferDetailsRepository.save(ctd);
//        newSmsHelper.sendMessage_4_1(p.getContract(), NewSmsHelper.SendType.SMS, p, getPealty(), ctd.getTransfereDetailsLink());
//        newSmsHelper.sendMessage_4_1(p.getContract(), NewSmsHelper.SendType.EMAIL, p, getPealty(), ctd.getTransfereDetailsLink());
//    }
//
//    private void sendMsg_4_1(List<Payment> payments) {
//
//        for (Payment p : payments) {
//            ClientTransferDetails ctd = new ClientTransferDetails();
//            ctd.setClient(p.getContract().getClient());
//            ctd.setContract(p.getContract());
//            ctd.setPayment(p);
//            ctd.setAmount(p.getAmountOfPayment());
//            ctd = clientTransferDetailsRepository.save(ctd);
//            newSmsHelper.sendMessage_4_1(p.getContract(), NewSmsHelper.SendType.SMS, p, getPealty(), ctd.getTransfereDetailsLink());
//            newSmsHelper.sendMessage_4_1(p.getContract(), NewSmsHelper.SendType.EMAIL, p, getPealty(), ctd.getTransfereDetailsLink());
//        }
//    }
//
//    private void sendMsg_4_2(List<Payment> _yesterday) {
//        for (Payment p : _yesterday) {
//            ClientTransferDetails ctd = new ClientTransferDetails();
//            ctd.setClient(p.getContract().getClient());
//            ctd.setContract(p.getContract());
//            ctd.setPayment(p);
//            ctd.setAmount(p.getAmountOfPayment());
//            ctd = clientTransferDetailsRepository.save(ctd);
//            newSmsHelper.sendMessage_4_2(p.getContract(),
//                    NewSmsHelper.SendType.SMS,
//                    p,
//                    getPealty(),
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName(),
//                    ctd.getTransfereDetailsLink());
//            newSmsHelper.sendMessage_4_2(p.getContract(),
//                    NewSmsHelper.SendType.EMAIL,
//                    p,
//                    getPealty(),
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName(),
//                    ctd.getTransfereDetailsLink());
//        }
//    }
//
//    private void sendMsg_4_3(List<Payment> _2daysAgo) {
//        for (Payment p : _2daysAgo) {
//            ClientTransferDetails ctd = new ClientTransferDetails();
//            ctd.setClient(p.getContract().getClient());
//            ctd.setContract(p.getContract());
//            ctd.setPayment(p);
//            ctd.setAmount(p.getAmountOfPayment());
//            ctd = clientTransferDetailsRepository.save(ctd);
//            newSmsHelper.sendMessage_4_3(p.getContract(),
//                    NewSmsHelper.SendType.SMS,
//                    p,
//                    getPealty(),
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName(),
//                    ctd.getTransfereDetailsLink());
//            newSmsHelper.sendMessage_4_3(p.getContract(),
//                    NewSmsHelper.SendType.EMAIL,
//                    p,
//                    getPealty(),
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName(),
//                    ctd.getTransfereDetailsLink());
//        }
//    }
//
//    private void sendMsg_4_4(List<Payment> _3daysAgo) {
//        for (Payment p : _3daysAgo) {
//
//            ClientTransferDetails ctd = new ClientTransferDetails();
//            ctd.setClient(p.getContract().getClient());
//            ctd.setContract(p.getContract());
//            ctd.setPayment(p);
//            ctd.setAmount(p.getAmountOfPayment());
//            ctd = clientTransferDetailsRepository.save(ctd);
//
//            newSmsHelper.sendMessage_4_4(p.getContract(),
//                    NewSmsHelper.SendType.SMS,
//                    p,
//                    getPaymentWithPenalty(p),
//                    getFourDaysAfterBouncing(p),
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName(),
//                    getPealty(),
//                    ctd.getTransfereDetailsLink());
//
//            newSmsHelper.sendMessage_4_4(p.getContract(),
//                    NewSmsHelper.SendType.EMAIL,
//                    p,
//                    getPaymentWithPenalty(p),
//                    getFourDaysAfterBouncing(p),
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName(),
//                    getPealty(),
//                    ctd.getTransfereDetailsLink());
//        }
//    }
//
//    private void sendMsg_4_5(List<Payment> _4daysAgo) {
//        for (Payment p : _4daysAgo) {
//
//            ClientTransferDetails ctd = new ClientTransferDetails();
//            ctd.setClient(p.getContract().getClient());
//            ctd.setContract(p.getContract());
//            ctd.setPayment(p);
//            ctd.setAmount(p.getAmountOfPayment());
//            ctd = clientTransferDetailsRepository.save(ctd);
//
//            newSmsHelper.sendMessage_4_5(p.getContract(),
//                    NewSmsHelper.SendType.SMS,
//                    getPaymentWithPenalty(p),
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName(),
//                    p,
//                    getPealty(),
//                    ctd.getTransfereDetailsLink());
//
//            newSmsHelper.sendMessage_4_5(p.getContract(),
//                    NewSmsHelper.SendType.EMAIL,
//                    getPaymentWithPenalty(p),
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName(),
//                    p,
//                    getPealty(),
//                    ctd.getTransfereDetailsLink());
//        }
//    }
//
//    private void sendMsg_4_6_1(List<Payment> _5daysAgo) {
//        for (Payment p : _5daysAgo) {
//            SmsExtendedLog log = SmsExtendedLogHelper.getNewLog(SmsTemplatesSetup.Message_4_6_1);
//            newSmsHelper.sendMessage_4_6_1(p.getContract(),
//                    NewSmsHelper.SendType.SMS,
//                    log,
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName());
//            newSmsHelper.sendMessage_4_6_1(p.getContract(),
//                    NewSmsHelper.SendType.YAYABOT,
//                    log,
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName());
//        }
//    }
//
//    private void sendMsg_4_6_2(List<Payment> _5daysAgo) {
//        for (Payment p : _5daysAgo) {
//
//            ClientTransferDetails ctd = new ClientTransferDetails();
//            ctd.setClient(p.getContract().getClient());
//            ctd.setContract(p.getContract());
//            ctd.setPayment(p);
//            ctd.setAmount(p.getAmountOfPayment());
//            ctd = clientTransferDetailsRepository.save(ctd);
//
//            newSmsHelper.sendMessage_4_6_2(p.getContract(),
//                    NewSmsHelper.SendType.SMS,
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName(),
//                    p,
//                    getPealty(),
//                    ctd.getTransfereDetailsLink());
//            newSmsHelper.sendMessage_4_6_2(p.getContract(),
//                    NewSmsHelper.SendType.EMAIL,
//                    p.getContract().getHousemaid() == null ? "your maid" : p.getContract().getHousemaid().getName(),
//                    p,
//                    getPealty(),
//                    ctd.getTransfereDetailsLink());
//        }
//    }
//
//    private void sendMsg_4_7(Payment p) {
//
//        newSmsHelper.sendMessage_4_7(p.getContract(),
//                NewSmsHelper.SendType.SMS);
//        newSmsHelper.sendMessage_4_7(p.getContract(),
//                NewSmsHelper.SendType.EMAIL);
//
//    }
//
//    private List<Payment> getBigChecks() {
//        PicklistItem ethiopian = PicklistHelper.getItem("nationalities", "ethiopian");
//        PicklistItem kenyan = PicklistHelper.getItem("nationalities", "kenyan");
//        PicklistItem filipino = PicklistHelper.getItem("nationalities", "philippines");
//
//        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
//        query.filterBy("typeOfPayment", "=", PicklistHelper.getItem(
//                AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
//                AccountingModule.PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT));
//        query.filterBy("status", "=", PaymentStatus.PDC);
//        query.filterBy(new SelectFilter(new SelectFilter("contract.housemaid.nationality.id", "=", ethiopian.getId())
//                .and("amountOfPayment", ">=", 6900.0))
//                .or(new SelectFilter("contract.housemaid.nationality.id", "=", kenyan.getId())
//                        .and("amountOfPayment", ">=", 6750.0))
//                .or(new SelectFilter("contract.housemaid.nationality.id", "=", filipino.getId())
//                        .and("amountOfPayment", ">=", 10500.0)));
//        query.filterBy("contract.isRenewal", "=", false);
//        query.filterBy("dateOfPayment", ">=", new Date());
//
//        Calendar c = Calendar.getInstance();
//        c.add(Calendar.MONTH, 1);
//        query.filterBy("dateOfPayment", "<", c.getTime());
//
//
//        return query.execute();
//    }
//
//    private void sendMsg_4_8(List<Payment> bigchecks) {
//        for (Payment p : bigchecks) {
//            newSmsHelper.sendMessage_4_8(p.getContract(),
//                    NewSmsHelper.SendType.SMS,
//                    p.getAmountOfPayment(),
//                    p.getDateOfPayment(),
//                    getWhatsappLink()
//            );
//            newSmsHelper.sendMessage_4_8(p.getContract(),
//                    NewSmsHelper.SendType.EMAIL,
//                    p.getAmountOfPayment(),
//                    p.getDateOfPayment(),
//                    getWhatsappLink()
//            );
//
//        }
//    }
//
//    private String getPealty() {
//        return Setup.getParameter(Setup.getModule("clientmgmt"), AccountingModule.PARAMETER_BOUNCED_PAYMENT_PENALTY);
//    }
//
//    private Double getPaymentWithPenalty(Payment p) {
//        return p.getAmountOfPayment() + Double.parseDouble(getPealty());
//    }
//
//    private Date getFourDaysAfterBouncing(Payment p) {
//        return DateUtil.addDays(p.getDateOfBouncing(), 4);
//    }
//
//    private String getWhatsappLink() {
//        String normalizedWANumber = StringHelper.NormalizePhoneNumber(Setup.getParameter(Setup.getModule("sales"),
//                AccountingModule.CC_RESOLVERS_PARAMETER));
//        return "https://wa.me/" + normalizedWANumber;
//    }
//
//    private boolean isJobEnabled() {
//        try {
//            return Boolean.parseBoolean(Setup.getParameter(Setup.getModule("clientmgmt"),
//                    AccountingModule.PARAMETER_BOUNCED_PAYMENTS_JOB_ENABLED));
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        }
//
//    }
//}
