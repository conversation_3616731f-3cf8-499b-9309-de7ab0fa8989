package com.magnamedia.scheduledjobs;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.util.ArrayMap;
import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRepository;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */

public class DdExpiredJob implements MagnamediaJob {

    private Map<String, Object> jobParameter;
    ObjectMapper mapper = new ObjectMapper();
    DirectDebitRepository directDebitRepository;
    DirectDebitFileRepository directDebitFileRepository;
    private DirectDebitController directDebitController;

    public DdExpiredJob() {
        directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        directDebitFileRepository = Setup.getRepository(DirectDebitFileRepository.class);
        directDebitController = Setup.getApplicationContext().getBean(DirectDebitController.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        loadJobParameter(true);
        if ((Boolean) jobParameter.get("active"))
            this.makeDdExpiredJob();
    }

    public void makeDdExpiredJob() {
        Date today = new DateTime().withTimeAtStartOfDay().toDate();
        String querySTR;
        String mainQuery;
        String directCountQuery;
        Page<DirectDebit> directDebits;
        SelectQuery<DirectDebit> query;
        Long maxId = 0L;
        Integer page = 0;
        // monthly direct debit
        do {
            mainQuery = "select @ from DirectDebit dd where " +
                    " (dd.status='" + DirectDebitStatus.PENDING + "' or dd.status='" + DirectDebitStatus.CONFIRMED + "') " +
                    " and ( dd.category = '" + DirectDebitCategory.B + "'" + " or " + "dd.category = '" + DirectDebitCategory.A + "' )" +
                    " and dd.expiryDate < '" + DateUtil.formatDateDashed(today) +
                    "' and dd.id > " + maxId + " order by dd.id";
            querySTR = mainQuery.replace("@", "dd");
            directCountQuery = mainQuery.replace("@", "count(dd)");
            query = new SelectQuery(querySTR, directCountQuery, DirectDebit.class, new ArrayMap<>());
            directDebits = query.execute(PageRequest.of(page, 100));
            maxId = applyChanges(directDebits, maxId);
        } while (directDebits.hasNext());

    }

    private Long applyChanges(Page<DirectDebit> directDebits, Long maxId) {
        for (DirectDebit dd : directDebits) {
            try {
                directDebitController.runDDExpiredJob(dd.getId());
            } catch (Exception e) {
                logger.log(Level.SEVERE, "error while update DD#" + dd.getId());
                logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
            } finally {
                maxId = Math.max(dd.getId(), maxId);
            }
        }

        return maxId;
    }

    private void loadJobParameter(boolean forceLoad) {
        if (jobParameter == null || forceLoad)
            try {
                jobParameter = mapper.readValue(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_MAKE_DD_EXPIRED_CONFIG_JOB), HashMap.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
    }
}
