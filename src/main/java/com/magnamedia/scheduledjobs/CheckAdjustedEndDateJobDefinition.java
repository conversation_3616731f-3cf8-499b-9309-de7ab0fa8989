package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.Contract;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.service.ContractService;
import org.joda.time.DateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.NoResultException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class CheckAdjustedEndDateJobDefinition implements MagnamediaJob {

    private ContractService contractService;
    @Override
    public void run(Map<String, ?> parameters) {
        contractService = Setup.getApplicationContext()
                        .getBean(ContractService.class);
        runForContracts();
    }

    @Transactional
    public void runForContracts(){
        Long maxId = 0L;

        List<Contract> result = this.getActiveContracts(maxId);
        while (result.size() > 0) {

            for (Contract c : result) {
                try {
                    contractService.fixAdjustedEndDate(c, false);
                } catch (Exception e) {
                    Setup.getMailService()
                            .sendEmail(EmailHelper.getRecipients(Setup.getParameter(Setup.getCurrentModule(),
                                            AccountingModule.PARAMETER_FAILED_BACKGROUND_TASKS_WITHIN_DAY_EMAILS)),
                                    new TextEmail("Fix Adjusted End Date Job Definition Error For Con_" + c.getId() + new DateTime().toString("yyyy-MM-dd HH:mm:ss"),
                                            e.getMessage()),
                                    EmailReceiverType.Office_Staff);
                }
            }

            maxId = result.get(result.size() - 1).getId();
            result = getActiveContracts(maxId);
        }
    }

    @Transactional
    public List<Contract> getActiveContracts(Long maxId) {
        SelectQuery<Contract> query = new SelectQuery(Contract.class);
        query.filterBy("status", "=", ContractStatus.ACTIVE);
        query.filterBy("id", ">", maxId);
        query.sortBy("id", true);
        Page<Contract> result;
        try {
            result = query.execute(PageRequest.of(0, 50));
        } catch (NoResultException e) {
            return new ArrayList<>();
        }

        return result.getContent();
    }
}