package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.report.PendingForCancellationDDsReport;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.service.MessagingService;
import org.joda.time.DateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.*;

/**
 * Created by Mamon.Masod on 1/27/2021.
 * Jirra ACC-2887
 */
public class DDPendingForCancellationSJ implements MagnamediaJob {

    private DirectDebitFileRepository ddfRepo;

    public DDPendingForCancellationSJ() {
        ddfRepo = Setup.getRepository(DirectDebitFileRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        Integer beforeNDays = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_PENDING_FOR_CANCELLATION_SJ_DAYS));
        Date targetDate = DateTime.now().withTimeAtStartOfDay().minusDays(beforeNDays).toDate();
        Page<DirectDebitFile> page;
        Integer pageIndex = 0;

        List<PendingForCancellationDDsReport.PendingForCancellationDDWrapper> toSendDDs = new ArrayList();
        do {
            page = ddfRepo.findByDdStatusAndStatusChangeDateBefore(DirectDebitStatus.PENDING_FOR_CANCELLATION, targetDate,
                    PageRequest.of(pageIndex++, 100));

            for (DirectDebitFile ddf : page.getContent()) {
                toSendDDs.add(new PendingForCancellationDDsReport.PendingForCancellationDDWrapper(ddf));
            }

        } while (page.hasNext());

        sendMail(toSendDDs, beforeNDays);
    }

    private void sendMail(List<PendingForCancellationDDsReport.PendingForCancellationDDWrapper> toSendDDs, Integer numOfDays) {
        if (toSendDDs == null || toSendDDs.isEmpty()) return;

        logger.info("DDs size: " + toSendDDs.size());

        PendingForCancellationDDsReport report = new PendingForCancellationDDsReport(toSendDDs);

        Map<String, String> parameters = new HashMap();
        parameters.put("num_of_days", numOfDays.toString());

        try {
            parameters.put("html_table", report.render());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("dd_pending_for_cancellation",
                        parameters, Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_DD_PENDING_FOR_CANCELLATION_SJ_EMAIL),
                        "DDs Pending for Cancellation for more than " + numOfDays + " days");
    }
}
