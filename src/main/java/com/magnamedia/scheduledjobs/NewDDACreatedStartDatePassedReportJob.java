package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.MailService;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.ReportMail;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.report.TemplatedReport;
import com.magnamedia.repository.DirectDebitFileRepository;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.LocalDate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class NewDDACreatedStartDatePassedReportJob implements TemplatedReport<DirectDebitFile>, MagnamediaJob {
    private static final Logger logger = Logger.getLogger(NewDDACreatedStartDatePassedReportJob.class.getName());
    private MailService mailService;
    private ReportMail reportMail;
    private DirectDebitFileRepository repository;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.SEVERE, "NewDDCreatedStartDatePassedReportJob start job");
        reportMail = Setup.getApplicationContext().getBean(ReportMail.class);
        mailService = Setup.getMailService();
        repository = Setup.getRepository(DirectDebitFileRepository.class);

        sendNewDdfCreatedStartDatePassedReport();

        logger.log(Level.SEVERE, "NewDDCreatedStartDatePassedReportJob end job");

    }


    public void sendNewDdfCreatedStartDatePassedReport() {
        if (getData().isEmpty())
            return;
        logger.log(Level.SEVERE, "start get recipients");
        String emails = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.NEW_DDA_CREATED_START_DATE_PASSED_REPORT_RECIPIENT);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
        if (recipients.isEmpty())
            return;
        logger.log(Level.SEVERE, "start send an email");

        StringBuilder tableBuilder = new StringBuilder("");

        try {
            tableBuilder.append(reportMail.toHtmlTableNoCount(getTable(), getHeaders(), getTitle(), 400));
            tableBuilder.append("<br/><hr/>");

        } catch (Throwable e) {
            tableBuilder.append("<p>").append(ExceptionUtils.getStackTrace(e)).append("</p>");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("title", getTitle());
        params.put("tableData", tableBuilder.toString());

        mailService.sendEmail(recipients,
                new TemplateEmail("Alert - ERP created an automatic DD with a start date that has passed already",
                        "NewDdaCreatedStartDatePassedEmail", params), null);
    }

    @Override
    public List<DirectDebitFile> getData() {
        return repository.findNewDdfCreatedStartDatePassed(new LocalDate().toDate());
    }

    @Override
    public String[] toStringArray(DirectDebitFile obj) {
        Contract contract = obj.getDirectDebit() != null ? obj.getDirectDebit().getContractPaymentTerm() != null ?
                obj.getDirectDebit().getContractPaymentTerm().getContract() : null : null  ;

        return new String[] {obj.getApplicationId(), new LocalDate(obj.getStartDate()).toString("dd/MM/yyyy"),
                contract != null ? contract.getId().toString() : "",
                contract != null ? contract.getClient().getName() : ""};
    }

    @Override
    public String getTitle() {
        return "The following automatic DDs were created with a start date that has passed already :";
    }

    @Override
    public String[] getHeaders() {
        return new  String[] {"DD Application Number", "DD start date", "Contract ID", "Client name"};
    }

    @Override
    public void refreshData() { }
}