package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.AccountingReportSetting;
import com.magnamedia.entity.projection.DdsAndCollectionComplianceCSVProjection;
import com.magnamedia.extra.DdsAndCollectionComplianceCSV;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.AccountingReportSettingRepository;
import org.joda.time.LocalDate;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.io.File;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

// ACC-6626
public class DdsAndCollectionComplianceJob implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(DdsAndCollectionComplianceJob.class.getName());
    private AccountingReportSettingRepository accountingReportSettingRepository;

    @Override
    public void run(Map<String, ?> map) {
        accountingReportSettingRepository = Setup.getRepository(AccountingReportSettingRepository.class);
        this.SendEmail();
    }

    public void SendEmail() {

        try {
            List<Object[]> data = getData();

            if (data.isEmpty()) return;
            logger.info("data size: " + data.size());

            List<DdsAndCollectionComplianceCSV> l = new ArrayList<>();
            int number = 1;
            for (Object[] issue : data) {             ;
                l.add(toDdsAndCollectionComplianceCSV(issue, number++));
            }

            List<EmailRecipient> recipients = EmailHelper.getRecipients(
                    Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_DDS_AND_COLLECTION_COMPLIANCE_EMAILS));
            if (recipients.isEmpty()) return;

            String[] headers = {"#", "Client ID", "Contract ID", "Issue Type", "Date"};
            String[] names = {"number", "clientId", "contractId", "issueType", "date"};

            File file = CsvHelper.generateCsv(l, DdsAndCollectionComplianceCSVProjection.class, headers, names,
                    "DDs and Collection Compliance Exceptions_" + new LocalDate().toString("yyyy-MM-dd"), ".csv");
            TextEmail mail = new TextEmail("DDs and Collection Compliance Exceptions", "");
            mail.addAttachement(file);
            Setup.getMailService().sendEmail(recipients, mail, EmailReceiverType.Office_Staff);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public DdsAndCollectionComplianceCSV toDdsAndCollectionComplianceCSV(Object[] obj, int number) {
        try {
            return new DdsAndCollectionComplianceCSV(number,
                    obj[0] == null ? "N\\A" : ((BigInteger) obj[0]).toString(),
                    obj[1] == null ? "N\\A" : ((BigInteger) obj[1]).toString(),
                    obj[2] == null ? "N\\A" : (String) obj[2],
                    obj[3] == null ? "N\\A" : new LocalDate((Date) obj[3]).toString("yyyy-MM-dd"));

        } catch (Exception e) {
            e.printStackTrace();
            return new DdsAndCollectionComplianceCSV(number, "N\\A", "N\\A", "N\\A", "N\\A");
        }
    }

    private List<Object[]> getData() {
        List<Object[]> data = new ArrayList<>();
        List<AccountingReportSetting> l = accountingReportSettingRepository
                .findByReportCategoryAndActiveTrue(AccountingReportSetting.ReportCategory.DDS_AND_COLLECTION_COMPLIANCE_EXCEPTIONS);
        l.stream()
                .filter(a-> a.getQuery() != null && !a.getQuery().isEmpty())
                .forEach(a -> data.addAll(executeNativeQuery(a.getQuery())));

        return data;
    }

    private List<Object[]> executeNativeQuery(String q) {
        EntityManager em = Setup.getEntityManagerFactory().createEntityManager();
        try {
            Query resultQuery = em.createNativeQuery(q);
            return resultQuery.getResultList();
        } catch (Exception e) {
            e.printStackTrace();
            Setup.getMailService()
                    .sendEmail(EmailHelper.getRecipients("<EMAIL>"),
                            new TextEmail("An error happened in DDs and Collection Compliance Exceptions",
                                    e.getMessage() + ": " + q),
                            EmailReceiverType.Office_Staff);
        } finally {
            em.close();
        }
        return new ArrayList<>();
    }
}
