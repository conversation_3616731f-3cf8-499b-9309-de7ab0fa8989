package com.magnamedia.scheduledjobs;

import com.magnamedia.controller.PaymentController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.PaymentRepository;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.joda.time.Days;
import org.joda.time.LocalDate;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Aug 24, 2020
 * Jirra CMA-154
 */

public class ExpectedWireTransferForReplacementJob implements MagnamediaJob {

    public static final Logger logger =

            Logger.getLogger(ExpectedWireTransferForReplacementJob.class.getName());


    private PaymentRepository paymentRepository;

    private PaymentController paymentController;

    @Override
    public void run(Map<String, ?> map) {

        // ACC-392
        int pausingDays = Integer.parseInt(
                Setup.getParameter(
                        Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_BOUNCING_FLOW_PAUSING_DAYS));
        this.paymentRepository = Setup.getRepository(PaymentRepository.class);
        this.paymentController = Setup.getApplicationContext().getBean(PaymentController.class);
        List<Payment> payments = paymentRepository.findByBouncedFlowPausedForReplacement(Boolean.TRUE);
        for (Payment p : payments) {
            logger.log(Level.SEVERE, "ExpextedWireTransferForReplacementJob payment id:" + p.getId());
            int days = Days.daysBetween(new LocalDate(p.getBouncedFlowPausedFromDate()), new LocalDate(new Date())).getDays();
            logger.log(Level.SEVERE, "ExpextedWireTransferForReplacementJob p date:" + new LocalDate(p.getBouncedFlowPausedFromDate()));
            logger.log(Level.SEVERE, "ExpextedWireTransferForReplacementJob today:" +  new LocalDate(new Date()));
            logger.log(Level.SEVERE, "ExpextedWireTransferForReplacementJob days:" + days);
            if (days >= pausingDays) {
                try {
                    boolean proceed = true;
                    // ACC-3968
                    Payment receivedPayment = paymentRepository.findByReplacementForAndStatus(p , PaymentStatus.RECEIVED);
                        if(receivedPayment != null){
                            proceed = false;
                        }
                       if(proceed)
                        paymentController.unPausePaymentMethod(p);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
