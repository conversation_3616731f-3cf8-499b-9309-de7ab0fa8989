package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.MailService;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.TenancyContract;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.ReportMail;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.report.TemplatedReport;
import com.magnamedia.repository.TenancyContractRepository;
import com.magnamedia.service.MessagingService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.thymeleaf.TemplateEngine;

import java.util.*;

/**
 * <AUTHOR>
 */
public class TenancyContractsEmailScheduledJob implements TemplatedReport<TenancyContract>, MagnamediaJob {

    private MailService mailService;

    private MessagingService messagingService;

    private TemplateEngine templateEngine;

    ReportMail reportMail;

    TenancyContractRepository repository;

    public TenancyContractsEmailScheduledJob() {
        mailService = Setup.getMailService();
        templateEngine = Setup.getApplicationContext().getBean(TemplateEngine.class);
        reportMail = Setup.getApplicationContext().getBean(ReportMail.class);
        repository = Setup.getRepository(TenancyContractRepository.class);
        messagingService = Setup.getApplicationContext().getBean(MessagingService.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.SendEmail();
        this.reminderTenancyContractWillExpiry();
    }

    @Override
    public String SendEmail() {

        // send within a day
        DateTime after = DateTime.now();
        DateTime before = after.plusDays(1);
        SendEmail(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_PDCS_CONTRACT_END_WITHIN_DAY_EMAILS),
                after.toDate(), before.toDate(),
                "Tenancy Contracts Ending Tomorrow",
                "Please note that the below contracts are ending within the next 24 hours");


        sleepTwoSeconds();

        // send within a week
        after = DateTime.now().plusDays(7).withMillisOfDay(0);
        before = after.plusDays(1).minusMillis(1);
        SendEmail(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_PDCS_CONTRACT_END_WITHIN_WEEK_EMAILS),
                after.toDate(), before.toDate(),
                "Tenancy Contracts Ending After a Week",
                "Please note that the below contracts are ending after a week");

        sleepTwoSeconds();

        // send within a month
        after = DateTime.now().plusDays(30).withMillisOfDay(0);
        before = after.plusDays(1).minusMillis(1);
        return SendEmail(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_PDCS_CONTRACT_END_WITHIN_MONTH_EMAILS),
                after.toDate(), before.toDate(),
                "Tenancy Contracts Ending After a Month",
                "Please note that the below contracts are ending after a month");
    }

    private void sleepTwoSeconds() {
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public String SendEmail(String emails, Date after, Date before, String subject, String title) {
        this.after = after;
        this.before = before;
        StringBuilder tableBuilder = new StringBuilder("");
        List<EmailRecipient> recipients = EmailHelper.getRecipients(emails);

        try {
            refreshData();
            tableBuilder.append(reportMail.toHtmlTable(getTable(), getHeaders(), title, 400));

            tableBuilder.append("<br/><hr/>");

        } catch (Throwable e) {
            tableBuilder.append("<p>").append(ExceptionUtils.getStackTrace(e)).append("</p>");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("title", title);
        params.put("tableData", tableBuilder.toString());

        if (recipients.size() > 0 && !getData().isEmpty()) {
            mailService.sendEmail(recipients,
                    new TemplateEmail(subject, "PDCsDueDateEmail", params), EmailReceiverType.Office_Staff);
        }
        return "";
    }

    Date after;
    Date before;
    private List<TenancyContract> data;

    @Override
    public List<TenancyContract> getData() {

        return data == null ? repository.findByEndDateBetweenAndActive(this.after, this.before, true) : data;
    }

    @Override
    public void refreshData() {
        data = null;
    }

    @Override
    public String[] toStringArray(TenancyContract obj) {
        return new String[]{
                obj.getName() == null ? "N\\A" : obj.getName(),
                obj.getEndDate()== null ? "N\\A" : TemplatedReport.format(obj.getEndDate()),
                obj.getDescription() == null ? "N\\A" : obj.getDescription()
        };
    }

    @Override
    public String getTitle() {
        return null;
    }

    @Override
    public String[] getHeaders() {
        return new String[]{
                "Benefeciary", "Contract End Date", "Property Description"
        };
    }

    public void reminderTenancyContractWillExpiry() {
        Long lastId = -1L;
        logger.info( "Start");

        String linkPage = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_FRONT_END_URL) + "#!/accounting/tenancy-contracts";
        Page<TenancyContract> contracts;

        do{
            SelectQuery<TenancyContract> query = new SelectQuery<>(TenancyContract.class);
            query.filterBy("id", ">", lastId);
            query.filterBy("active", "=", true);

            SelectFilter beforeTwoWeeks = new SelectFilter("notifyDate", "=", TenancyContract.NotifyType.BEFORE_TWO_WEEKS);
            beforeTwoWeeks.and(new SelectFilter("endDate", "in", Arrays.asList(
                    java.sql.Date.valueOf(new LocalDate().plusWeeks(1).toString("yyyy-MM-dd")),
                    java.sql.Date.valueOf(new LocalDate().plusWeeks(2).toString("yyyy-MM-dd")))));

            SelectFilter beforeTwoMonths = new SelectFilter("notifyDate", "=", TenancyContract.NotifyType.BEFORE_TWO_MONTHS);
            beforeTwoMonths.and(new SelectFilter("endDate", "in", Arrays.asList(
                    java.sql.Date.valueOf(new LocalDate().plusMonths(1).toString("yyyy-MM-dd")),
                    java.sql.Date.valueOf(new LocalDate().plusMonths(2).toString("yyyy-MM-dd")))));

            SelectFilter beforeThreeMonths = new SelectFilter("notifyDate", "=", TenancyContract.NotifyType.BEFORE_THREE_MONTHS);
            beforeThreeMonths.and(new SelectFilter("endDate", "in", Arrays.asList(
                    java.sql.Date.valueOf(new LocalDate().plusMonths(1).toString("yyyy-MM-dd")),
                    java.sql.Date.valueOf(new LocalDate().plusMonths(2).toString("yyyy-MM-dd")),
                    java.sql.Date.valueOf(new LocalDate().plusMonths(3).toString("yyyy-MM-dd")))));

            SelectFilter beforeSixMonths = new SelectFilter("notifyDate", "=", TenancyContract.NotifyType.BEFORE_SIX_MONTHS);
            beforeSixMonths.and(new SelectFilter("endDate", "in", Arrays.asList(
                    java.sql.Date.valueOf(new LocalDate().plusMonths(1).toString("yyyy-MM-dd")),
                    java.sql.Date.valueOf(new LocalDate().plusMonths(2).toString("yyyy-MM-dd")),
                    java.sql.Date.valueOf(new LocalDate().plusMonths(3).toString("yyyy-MM-dd")),
                    java.sql.Date.valueOf(new LocalDate().plusMonths(4).toString("yyyy-MM-dd")),
                    java.sql.Date.valueOf(new LocalDate().plusMonths(5).toString("yyyy-MM-dd")),
                    java.sql.Date.valueOf(new LocalDate().plusMonths(6).toString("yyyy-MM-dd")))));

            query.filterBy(beforeTwoWeeks.or(beforeTwoMonths).or(beforeThreeMonths).or(beforeSixMonths));
            query.sortBy("id", true, false);
            contracts = query.execute(PageRequest.of(0, 200));
            contracts.getContent().forEach(c -> {
                logger.info( "Tenancy Contract Id: " + c.getId());
                sendEmail(c, linkPage);
            });

            if(!contracts.getContent().isEmpty()){
                lastId = contracts.getContent().get(contracts.getContent().size() - 1).getId();
            }

        } while(!contracts.getContent().isEmpty());

        logger.info( "End");
    }

    public void sendEmail(TenancyContract tenancyContract, String linkPage) {

        if(tenancyContract.getPrimaryPerson().getEmail() == null) return;

        Map<String, String> params = new HashMap<>();
        params.put("tenancy_contract_name", tenancyContract.getName());
        params.put("tenancy_contract_expiry_date", tenancyContract.getEndDate().toString());
        params.put("link_of_the_page", linkPage);

        try {
            messagingService.sendEmailToOfficeStaffWithCc(tenancyContract.getPrimaryPerson(),
                    "alert_of_tenancy_contract_expiry_date_email", params,
                    tenancyContract.getPrimaryPerson().getEmail(),
                    tenancyContract.getSecondaryPerson() == null || tenancyContract.getSecondaryPerson().getEmail() == null ?
                            new ArrayList<>() : Collections.singletonList(tenancyContract.getSecondaryPerson().getEmail()),
                    "Alert of " + tenancyContract.getName() + " Expiry Date");
        } catch (RuntimeException e) {
            e.printStackTrace();
        }
    }
}
