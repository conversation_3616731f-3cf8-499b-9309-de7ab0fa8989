package com.magnamedia.scheduledjobs;


import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.service.MessagingService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */

public class SendReminderToApproveExpenseTodo implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(SendReminderToApproveExpenseTodo.class.getName());

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "start");

        String qStr= "select distinct u.email from ExpenseRequestTodo e " +
                     "inner join User u on e.approveHolder = u " +
                     "where e.taskName = 'WAITING_MANAGER_APPROVAL' and u.email is not null and u.email <> ''";

        List<String> emailsList = new SelectQuery<>(qStr, "", String.class, new HashMap<>()).execute();

        if (emailsList.isEmpty()) {
            logger.log(Level.INFO, "there are no pending todo");
            return;
        }

        String subject = "Pending expenses to-dos waiting for your approval";

        Map<String, String> parameters = new HashMap();
        String baseURL = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) + "#!/accounting/";
        parameters.put("link", baseURL + "expenses-approvals");

        emailsList.forEach(e -> {
            logger.log(Level.INFO, "email: {0}", e);

            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaff("approve_expense_remind_manager", parameters, e, subject);
        });

        logger.log(Level.INFO, "end");
    }
}
