package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.Payment;
import static com.magnamedia.helper.PicklistHelper.getItem;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.HousemaidRepository;
import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> <PERSON> <<EMAIL>>
 */

// ACC-369

@BusinessRule(moduleCode = "", entity = Payment.class, 
        events = {BusinessEvent.AfterCreate},
        fields = {"id", "typeOfPayment.id", "status", "contract.id", "dateOfPayment"})
public class MaidVisaPaymentBusinessRule implements BusinessAction<Payment>{
    
    
    private static final Logger logger =
            Logger.getLogger(MaidVisaPaymentBusinessRule.class.getName());
    
    @Override
    public boolean validate(Payment payment, BusinessEvent businessEvent) {
        
        logger.log(Level.SEVERE, "MaidVisaPaymentBusinessRule Validation.");
        PicklistItem monthlyPayment = getItem(AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                AccountingModule.PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT);
        
        return monthlyPayment.getId().equals(payment.getTypeOfPayment().getId()) // Type of payment is monthly payment
                && payment.getStatus() == PaymentStatus.RECEIVED;                 // Payment status is received
    }

    @Override
    public Map<String, Object> execute(Payment payment, BusinessEvent businessEvent) {
        
        logger.log(Level.SEVERE, "MaidVisaPaymentBusinessRule execute.");
        if (payment != null && payment.getContract() != null){
            ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);   
            Contract c = contractRepository.findOne(payment.getContract().getId());
            Housemaid housemaid = c.getHousemaid();

            if( housemaid!=null
                    && housemaid.getStartDate() == null
                    && isMaidVisa(housemaid)) {                                        // The maid is maid-visa

                HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);
                LocalDate newStartDate = payment.getDateOfPayment().toLocalDate().plusMonths(1).withDayOfMonth(1); // The first of next month of payment date
                housemaid.setStartDate(Date.valueOf(newStartDate));
                housemaidRepository.save(housemaid);
            }
        }
        logger.log(Level.SEVERE, "MaidVisaPaymentBusinessRule execute end.");
        return null;
    }
    
    // Function returns true iff the housemaid is a maid-visa
    private static boolean isMaidVisa(Housemaid maid) {
        List<Contract> maidsContracts = Setup.getRepository(ContractRepository.class).findByHousemaid(maid);//maid.getContracts();
        PicklistItem maidVisa = getItem(AccountingModule.PICKLIST_PROSPECTTYPE, "maidvisa.ae_prospect");
        if(maidsContracts != null && maidsContracts.size() > 0) {
            List<Contract> activeContracts = maidsContracts.stream().
                    filter((activeContract) -> ContractStatus.ACTIVE.equals(activeContract.getStatus())).
                    collect(Collectors.toList());
            if(activeContracts.size() > 0) {
                return activeContracts.get(0).getContractProspectType() != null &&
                        activeContracts.get(0).getContractProspectType().getId().equals(maidVisa.getId());
            }
        }
        return false;
    }  
    
}
