package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.DirectDebitConfiguration;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.DirectDebitConfigurationRepository;

import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by Mamon.Masod on 5/2/2021.
 */
@BusinessRule(entity = PicklistItem.class, events = {BusinessEvent.AfterCreate}, fields = {"id", "list.code"})
public class BankPickListItemBR implements BusinessAction<PicklistItem> {
    private static final Logger logger = Logger.getLogger(BankPickListItemBR.class.getName());

    @Override
    public boolean validate(PicklistItem entity, BusinessEvent event) {
        logger.info("validation");

        return entity.getList() != null && entity.getList().getCode() != null && entity.getList().getCode().equals(AccountingModule.PICKLIST_BANK_NAME);
    }

    @Override
    public Map execute(PicklistItem entity, BusinessEvent event) {
        logger.info("execute");

        DirectDebitConfiguration ddConfiguration = DirectDebitConfiguration.newInstance(entity);
        Setup.getRepository(DirectDebitConfigurationRepository.class).save(ddConfiguration);

        return null;
    }
}
