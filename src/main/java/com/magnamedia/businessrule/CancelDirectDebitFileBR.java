package com.magnamedia.businessrule;

import com.magnamedia.controller.DDFBatchForRPAController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 11, 2020
 *         Jirra ACC-2225
 */

@BusinessRule(moduleCode = "", entity = DirectDebitFile.class,
        events = {BusinessEvent.AfterUpdate},
        fields = { "id", "ddStatus", "ddMethod", "directDebit.id", "directDebit.category", "forceUpdate" })
public class CancelDirectDebitFileBR implements BusinessAction<DirectDebitFile> {

    private static final Logger logger =
            Logger.getLogger(CancelDirectDebitFileBR.class.getName());

    @Override
    public boolean validate(DirectDebitFile entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "CancelDirectDebitFileBR Validation.");

        if (entity.getDdStatus() == null || entity.getDirectDebit() == null || entity.getDirectDebit().getId() == null) {
            return false;
        }

        HistorySelectQuery<DirectDebitFile> historyQuery = new HistorySelectQuery<>(DirectDebitFile.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("ddStatus");
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);

        List<DirectDebitFile> oldDirectDebitFiles = historyQuery.execute();
        DirectDebitFile oldDirectDebitFile = null;

        logger.log(Level.SEVERE, "CancelDirectDebitFileBR execute dd id:" + entity.getDirectDebit().getId());
        logger.log(Level.SEVERE, "CancelDirectDebitFileBR execute dd category:" + entity.getDirectDebit().getCategory());
        logger.log(Level.SEVERE, "CancelDirectDebitFileBR execute ddf id:" + entity.getId());
        logger.log(Level.SEVERE, "CancelDirectDebitFileBR execute ddf status:" + entity.getDdStatus());


        if (oldDirectDebitFiles != null && !oldDirectDebitFiles.isEmpty()) {
            oldDirectDebitFile = oldDirectDebitFiles.get(0);
        }

        logger.log(Level.SEVERE, "CancelDirectDebitFileBR execute ddf old status:" +
                (oldDirectDebitFile != null ? oldDirectDebitFile.getDdStatus() : "old ddf is null"));

        return ((oldDirectDebitFile == null || oldDirectDebitFile.getDdStatus() == null || !oldDirectDebitFile.getDdStatus().equals(DirectDebitStatus.CANCELED)) &&
                entity.getDdStatus().equals(DirectDebitStatus.CANCELED));
    }

    @Override
    public Map<String, Object> execute(DirectDebitFile entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "CancelDirectDebitFileBR execute.");
        DirectDebitRepository ddRepo = Setup.getRepository(DirectDebitRepository.class);
        DirectDebitFileRepository ddfRepo = Setup.getRepository(DirectDebitFileRepository.class);
        DDFBatchForRPAController ddfBatchForRPAController = Setup.getApplicationContext().getBean(DDFBatchForRPAController.class);
        DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository =
                Setup.getApplicationContext().getBean(DirectDebitRejectionToDoRepository.class);

        DirectDebit dd = ddRepo.findOne(entity.getDirectDebit().getId());
        if(dd == null)
            return null;

        //Jirra ACC-2792
        boolean isMainDDf = (entity.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) && dd.getAutoDdfFile() != null && dd.getAutoDdfFile().getId() != null &&
                dd.getAutoDdfFile().getId().equals(entity.getId())) ||
                (entity.getDdMethod().equals(DirectDebitMethod.MANUAL) && dd.getManualDdfFile() != null && dd.getManualDdfFile().getId() != null &&
                        dd.getManualDdfFile().getId().equals(entity.getId()));
        
        boolean oldIsPendingForCancelation = (entity.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) && dd.getStatus().equals(DirectDebitStatus.PENDING_FOR_CANCELLATION)) ||
                (entity.getDdMethod().equals(DirectDebitMethod.MANUAL) && dd.getMStatus().equals(DirectDebitStatus.PENDING_FOR_CANCELLATION));

        //Jirra ACC-ACC-2225
        if (!ddfRepo.existsByDirectDebitAndDdStatusNotInAndIdNotInAndDdMethod(
                entity.getDirectDebit(),
                oldIsPendingForCancelation ? Arrays.asList(DirectDebitStatus.CANCELED, DirectDebitStatus.EXPIRED, DirectDebitStatus.REJECTED) : Arrays.asList(DirectDebitStatus.CANCELED),
                Arrays.asList(entity.getId()), entity.getDdMethod())
                    || isMainDDf) {

            if (dd.getCategory() == DirectDebitCategory.A) {
                logger.log(Level.SEVERE, "CancelDirectDebitFileBR execute cancellation for DDA");
                dd.setStatus(DirectDebitStatus.CANCELED);
                dd.setMStatus(DirectDebitStatus.CANCELED);
                dd.setAutoDdfFile(null);
                dd.setManualDdfFile(null);
                ddRepo.save(dd);
            } else if (dd.getCategory() == DirectDebitCategory.B) {

                boolean saveDD = false;
                if (entity.getDdMethod() == DirectDebitMethod.AUTOMATIC) {
                    logger.log(Level.SEVERE, "CancelDirectDebitFileBR execute cancellation for automatics");
                    dd.setStatus(DirectDebitStatus.CANCELED);
                    dd.setAutoDdfFile(null);
                    saveDD = true;
                }

                //Jirra ACC-3296, after 3296 DDB may has no Manuals
                boolean hasNoManualDDFs = !ddfRepo.existsByDirectDebitAndDdMethod(entity.getDirectDebit(), DirectDebitMethod.MANUAL);

                if (entity.getDdMethod() == DirectDebitMethod.MANUAL || hasNoManualDDFs) {
                    logger.log(Level.SEVERE, "CancelDirectDebitFileBR execute cancellation for manuals");
                    dd.setMStatus(DirectDebitStatus.CANCELED);
                    dd.setManualDdfFile(null);
                    saveDD = true;
                }

                if (saveDD){
                    ddRepo.save(dd);
                    if (dd.getStatus().equals(DirectDebitStatus.CANCELED)
                            && dd.getMStatus().equals(DirectDebitStatus.CANCELED)){
                        if (dd.getDirectDebitBouncingRejectionToDo() != null
                                &&dd.getDirectDebitBouncingRejectionToDo().getId() != null){
                            DirectDebitRejectionToDo toDo = dd.getDirectDebitBouncingRejectionToDo();
                            toDo.setStopped(true);
                            toDo.setCompleted(true);
                            toDo.setDontSendDdMessage(true);
                            directDebitRejectionToDoRepository.save(toDo);
                        }
                        if (dd.getDirectDebitRejectionToDo() != null
                                &&dd.getDirectDebitRejectionToDo().getId() != null){
                            DirectDebitRejectionToDo toDo = dd.getDirectDebitRejectionToDo();
                            DirectDebit rejectedDirectDebit = toDo.getLastDirectDebit();
                            if (dd.getId().equals(rejectedDirectDebit.getId())){
                                toDo.setStopped(true);
                                toDo.setCompleted(true);
                                toDo.setDontSendDdMessage(true);
                                directDebitRejectionToDoRepository.save(toDo);
                            }
                        }
                    }
                }
            }
        } else {
            logger.log(Level.SEVERE, "there is a not canceled file on DD type: " + entity.getDirectDebit().getCategory() + " , method: " + entity.getDdMethod());
        }

        //Jirra ACC-2761
        try {
            ddfBatchForRPAController.excludeDDFFromRPABatch(entity);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Exception while excluding the DDF fromm RPA_BATCH");
            logger.log(Level.SEVERE, "Exception: " + ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e.getMessage());
        }

        logger.log(Level.SEVERE, "CancelDirectDebitFileBR execute ends");

        return null;
    }

}
