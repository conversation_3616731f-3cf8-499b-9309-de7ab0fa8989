/*
package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.DirectDebitCancellationService;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.service.OecAmendDDsService;
import com.magnamedia.service.SwitchingBankAccountService;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBCaseDRejectionWaitingBankResponseStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBRejectionWaitingBankResponseStep;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
*/
/**
 * <AUTHOR>
 *         Created on 7,4, 2020
 *         Jira ACC-1591
 *//*


//@BusinessRule(moduleCode = "", entity = DirectDebitFile.class,
//        events = {BusinessEvent.AfterUpdate},
//        fields = {"id", "status", "ddStatus", "ddMethod", "directDebit.status", "directDebit.MStatus", "directDebit.id"})
public class DirectDebitFileAcceptedBusinessRule { // implements BusinessAction<DirectDebitFile> {

    private static final Logger logger =
            Logger.getLogger(DirectDebitFileAcceptedBusinessRule.class.getName());

//    @Override
    public boolean validate(DirectDebitFile entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule Validation.");
        
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        DirectDebit dd = directDebitRepository.findOne(entity.getDirectDebit().getId());

        if (dd != null){
            logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule dd id: " + dd.getId());
            logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule dd status: " + dd.getStatus());
        }
        logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule file id: " + entity.getId());
        logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule file getDdMethod: " + entity.getDdMethod());
        logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule file status: " + entity.getStatus());
        logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule file ddStatus: " + entity.getDdStatus());

        return (dd != null
                && dd.getStatus() != null
                && ((entity.getDdMethod() == DirectDebitMethod.AUTOMATIC && dd.getStatus().equals(DirectDebitStatus.PENDING))
                || (entity.getDdMethod() == DirectDebitMethod.MANUAL && dd.getMStatus().equals(DirectDebitStatus.PENDING)))
                && entity.getDdStatus() != null
                && entity.getDdStatus().equals(DirectDebitStatus.CONFIRMED));
    }

//    @Override
    public Map<String, Object> execute(DirectDebitFile entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule execute.");

        Map<String, Object> map = new HashMap();

        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        DirectDebit dd = directDebitRepository.findOne(entity.getDirectDebit().getId());
        DirectDebitRejectionFlowService directDebitRejectionFlowService = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);

        DirectDebitConfiguration ddConfiguration = dd.getDdConfiguration();

        if (entity.getDdMethod() == DirectDebitMethod.AUTOMATIC) {
            logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule automatic file");

            dd.setStatus(DirectDebitStatus.CONFIRMED);
            dd.setAutoDdfFile(entity);

            Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                .cancelOtherAutoApprovedDDs(dd);

            boolean allManualRejected = directDebitRejectionFlowService.allManualFilesRejected(dd.getDirectDebitFiles());

            logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule allManualRejected: " + allManualRejected);
            logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule dd.getMStatus(): " + dd.getMStatus());

            if (dd.getMStatus() == DirectDebitStatus.CONFIRMED || !dd.isGenerateManualDDFsFromConfig()) {  // at least one manual is approved -> one auto is approved and one manual is approved
                if (dd.getDirectDebitRejectionToDo() != null) {
                    DirectDebitRejectionToDo rejectTodo = dd.getDirectDebitRejectionToDo();
                    rejectTodo.setStopped(true);
                    rejectTodo.setCompleted(true);
                    DirectDebitRejectionToDoRepository repository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);
                    repository.save(rejectTodo);
                }
                // ACC-2784
//                PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
//                List<PaymentStatus> statuses = new ArrayList<>();
//                statuses.add(PaymentStatus.PDC);
//                List<Payment> payments = paymentRepository.findByDirectDebitIdAndStatusIn(dd.getId(), statuses);
//
//                if (payments.size() > 0) {
//                    payments.sort(new Comparator<Payment>() {
//                        @Override
//                        public int compare(Payment payment, Payment t1) {
//                            return payment.getDateOfPayment().compareTo(t1.getDateOfPayment());
//                        }
//                    });
//                    map.put("startDate", payments.get(0).getDateOfPayment());
//                }

            } else if (allManualRejected) { // all manual rejected -> one auto is approved all manual rejected
                logger.info("allManualRejected");
                if (ddConfiguration.isIncludeManualInDDBFlow()) {
                    logger.info("DD Configuration, isIncludeManualInDDBFlow");
                    startRejectionFlow(dd);
                }
                else if (dd.getDirectDebitRejectionToDo() != null) {
                    DirectDebitRejectionToDo rejectTodo = dd.getDirectDebitRejectionToDo();
                    rejectTodo.setStopped(true);
                    rejectTodo.setCompleted(true);
                    DirectDebitRejectionToDoRepository repository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);
                    repository.save(rejectTodo);
                    dd.setMStatus(DirectDebitStatus.REJECTED);
                    dd.setManualDdfFile(null);
                }
            }
            // else do nothing because there is no approved manual file yet and not all manuals are rejected
            dd = directDebitRepository.save(dd);
            
        } else if (entity.getDdMethod() == DirectDebitMethod.MANUAL) {
            logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule manual file");

            dd.setMStatus(DirectDebitStatus.CONFIRMED);
            dd.setManualDdfFile(entity);

            Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                .cancelOtherManualApprovedDDs(dd);

            if (dd.getCategory() == DirectDebitCategory.A) {
                if (dd.getDirectDebitRejectionToDo() != null) {
                    DirectDebitRejectionToDo rejectTodo = dd.getDirectDebitRejectionToDo();
                    rejectTodo.setStopped(true);
                    rejectTodo.setCompleted(true);
                    DirectDebitRejectionToDoRepository repository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);
                    repository.save(rejectTodo);
                }
            } else if (dd.getCategory() == DirectDebitCategory.B) {
                boolean allAutoRejected = directDebitRejectionFlowService.allAutoFilesRejected(dd.getDirectDebitFiles());

                if (dd.getStatus() == DirectDebitStatus.CONFIRMED) {  // at least one Auto is approved -> one manual is approved one auto is approved
                    if (dd.getDirectDebitRejectionToDo() != null) {
                        DirectDebitRejectionToDo rejectTodo = dd.getDirectDebitRejectionToDo();
                        rejectTodo.setStopped(true);
                        rejectTodo.setCompleted(true);
                        DirectDebitRejectionToDoRepository repository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);
                        repository.save(rejectTodo);
                    }
                } else if (allAutoRejected) { // all Auto rejected -> one manual is approved all auto is rejected
                    startRejectionFlow(dd);
                }     
                // else  do nothing because there is no approved Auto file yet and not all Auto are rejected
            }
            
            dd = directDebitRepository.saveAndFlush(dd);
            
            // ACC-4053 process bounced payment reminders for both A & B DDs
            List<Payment> bouncedPayments = Setup.getRepository(PaymentRepository.class)
                    .findByDirectDebitIdAndStatusAndReplaced(dd.getId(), PaymentStatus.BOUNCED, false);

            for (Payment bouncedPayment : bouncedPayments) {
                logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule bounced payment id: " + bouncedPayment.getId());
                logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule bounced payment trials: " + bouncedPayment.getTrials());

                Map<String, Object> paymentMap = new HashMap();
                paymentMap.put("id", bouncedPayment.getId());
                paymentMap.put("trials", bouncedPayment.getTrials() > 0 ? 0 : -1);

                try {
                    Setup.getApplicationContext().getBean(InterModuleConnector.class)
                            .postJsonAsync("clientmgmt/Payments/update", paymentMap);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Logger.getLogger(DirectDebitFileAcceptedBusinessRule.class.getName()).log(Level.SEVERE, null, ex);
                    throw new RuntimeException("Problem with accounting Payment Update" + ex.getMessage());
                }
            }
        }

        SwitchingBankAccountService switchingBankAccountService = Setup.getApplicationContext().getBean(SwitchingBankAccountService.class);
        if (switchingBankAccountService.isClientSwitchingBankAccount(dd)) {
            handleSwitchingBankAccount(dd);
        }
        if(dd.getCategory().equals(DirectDebitCategory.A) && 
                dd.getImageForDD()!=null && dd.getImageForDD().getId() != null){//ACC-4031
            
            switchingBankAccountService.handleConfirmNewDDASwitchingBankAccount(dd);
        }

        //Jira ACC-2956
        closeRelatedNotifications(dd);

        //Jirra ACC-3597
        OecAmendDDsService oecAmendDDsService =  Setup.getApplicationContext().getBean(OecAmendDDsService.class);
        oecAmendDDsService.handleOecFlow(dd);
        logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule execute end.");
        
        return map;
    }

    private void startRejectionFlow(DirectDebit directDebit) {
        logger.log(Level.SEVERE, "DirectDebitFileAcceptedBusinessRule startRejectionFlow for: " + directDebit.getId());

        DirectDebitRejectionToDo directDebitRejectionToDo = getDirectDebitToDo(directDebit);
        if (directDebitRejectionToDo != null && !directDebitRejectionToDo.isStopped() && !directDebitRejectionToDo.isCompleted()) {
            List<String> currentTasks = directDebitRejectionToDo.getCurrentTasks();
            if (!currentTasks.isEmpty()) {
                DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTasks.get(currentTasks.size() - 1));
                switch (step) {
                    case WAITING_BANK_RESPONSE_B: {
                        DirectDebitBRejectionWaitingBankResponseStep waitingBankResponseStepB = Setup.getApplicationContext().getBean(DirectDebitBRejectionWaitingBankResponseStep.class);
                        waitingBankResponseStepB.onDone(directDebitRejectionToDo);
                        break;
                    }
                    case WAITING_BANK_RESPONSE_B_CASE_D: {
                        DirectDebitBCaseDRejectionWaitingBankResponseStep waitingBankResponseStepBCaseD
                                = Setup.getApplicationContext().getBean(DirectDebitBCaseDRejectionWaitingBankResponseStep.class);
                        waitingBankResponseStepBCaseD.onDone(directDebitRejectionToDo);
                        break;
                    }
                }
            }
        }
    }

    private DirectDebitRejectionToDo getDirectDebitToDo(DirectDebit entity) {
        DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);
        DirectDebitRejectionToDo directDebitRejectionToDo = entity.getDirectDebitRejectionToDo();

        if (directDebitRejectionToDo == null) {
            DirectDebitRejectionFlowService service = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);
            service.startFlow(entity);
            return null;
        }

        directDebitRejectionToDo.setLastDirectDebit(entity);
        directDebitRejectionToDo = directDebitRejectionToDoRepository.getOne(directDebitRejectionToDo.getId());

        return directDebitRejectionToDo;
    }

    private void handleSwitchingBankAccount(DirectDebit dd) {
        SwitchingBankAccountService switchingBankAccountService = Setup.getApplicationContext().getBean(SwitchingBankAccountService.class);
        ContractPaymentTermRepository cptRepo = Setup.getRepository(ContractPaymentTermRepository.class);
        ContractPaymentTerm oldCPT = dd.getContractPaymentTerm();
        Contract contract = oldCPT.getContract();

        ContractPaymentTerm newCPT = cptRepo.findFirstByContractAndIdGreaterThanOrderById(contract, oldCPT.getId());

        List<DirectDebit> directDebits = switchingBankAccountService.generateNewDDsOnSwitching(newCPT, Arrays.asList(dd), null);

        switchingBankAccountService.signDDsAfterSwitching(newCPT, oldCPT);
    }

    private void closeRelatedNotifications(DirectDebit directDebit) {
        if (directDebit.getRejectCategory() != null && directDebit.getRejectCategory().equals(DirectDebitRejectCategory.Authorization)) {
            Contract contract = directDebit.getContractPaymentTerm().getContract();

            PushNotificationHelper pushNotificationHelper = Setup.getApplicationContext().getBean(PushNotificationHelper.class);

            List<PushNotification> notifications = pushNotificationHelper.getByOwnerTypeAndId("OnCloseTaxiWorkOrder", contract.getId());
            notifications.addAll(pushNotificationHelper.getByOwnerTypeAndId("Contract", contract.getId()));

            pushNotificationHelper.stopDisplaying(notifications);
        }
    }
}
*/
