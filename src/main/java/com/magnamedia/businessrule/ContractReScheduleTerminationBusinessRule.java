package com.magnamedia.businessrule;


import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DirectDebitCancelationToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.PaymentService;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.magnamedia.helper.PicklistHelper.getItem;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 7, 2020
 *         Jirra ACC-1135
 *         final flow
 */

@BusinessRule(moduleCode = "clientmgmt", entity = Contract.class,
        events = {BusinessEvent.AfterUpdate},
        fields = {"id", "scheduledDateOfTermination"})
public class ContractReScheduleTerminationBusinessRule implements BusinessAction<Contract> {

    private static final Logger logger =
            Logger.getLogger(ContractReScheduleTerminationBusinessRule.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Contract entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "ContractReScheduleTerminationBusinessRule Validation.");
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        Contract old = contractRepository.findOne(entity.getId());

        logger.log(Level.SEVERE, prefix + "contract code: " + old.getContractProspectType().getCode());
        logger.log(Level.SEVERE, prefix + "old scheduled date of termination: " + old.getScheduledDateOfTermination());
        logger.log(Level.SEVERE, prefix + "new scheduled date of termination: " + entity.getScheduledDateOfTermination());

        return old.getContractProspectType().getCode().equals(
                PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))
                && entity.getScheduledDateOfTermination() != null
                && old.getScheduledDateOfTermination() != null
                && old.getScheduledDateOfTermination().compareTo(entity.getScheduledDateOfTermination()) != 0;
    }

    @Override
    public Map<String, Object> execute(Contract entity, BusinessEvent even) {
        Map<String, Object> map = new HashMap();
        logger.log(Level.SEVERE, "ContractUnDoTerminationBusinessRule execute.");
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        DirectDebitCancelationToDoRepository debitCancelationToDoRepository = Setup.getRepository(DirectDebitCancelationToDoRepository.class);

        Contract old = contractRepository.findOne(entity.getId());

        Calendar oldDateCal = Calendar.getInstance();
        Calendar newDateCal = Calendar.getInstance();
        oldDateCal.setTime(new Date());
        newDateCal.setTime(entity.getScheduledDateOfTermination());

        // Same CURRENT month -> do nothing
        if (newDateCal.get(Calendar.MONTH) > oldDateCal.get(Calendar.MONTH) ||
                newDateCal.get(Calendar.YEAR) > oldDateCal.get(Calendar.YEAR)) {

            // we owes refund to the client
            PicklistItem monthlyPayment = getItem(AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                    AccountingModule.PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT);

            SelectQuery<Payment> selectQuery = new SelectQuery(Payment.class);
            selectQuery.filterBy("contract", "=", old)
                    .and("bouncedInCancellationWaitingPeriod", "=", true)
                    .and("status", "=", PaymentStatus.DELETED)
                    .and("typeOfPayment", "=", monthlyPayment);

            List<Payment> deletedPayments = selectQuery.execute();
            logger.log(Level.SEVERE, prefix + "deleted payments size: " + deletedPayments.size());

            // Change any DELETED payment flagged as AUTO_DELETED to BOUNCED and Delete the flag
            deletedPayments.forEach(payment -> {
                logger.info("bouncing payment with id: " + payment.getId());
                payment.setStatus(PaymentStatus.BOUNCED);
                payment.setBouncedInCancellationWaitingPeriod(false);
                paymentService.forceUpdatePayment(payment);
            });

            // Close any to-do to Cancel Future DDs
            List<DirectDebit> dds = directDebitRepository.findByContractPaymentTerm_Contract(old);
            if (dds != null) {
                for (DirectDebit dd : dds) {
                    List<DirectDebitCancelationToDo> cancelationToDos = debitCancelationToDoRepository.
                            findByDirectDebitFile_DirectDebitAndCompletedFalseAndStoppedFalse(dd);
                    if (cancelationToDos != null) {
                        for (DirectDebitCancelationToDo cancelationToDo : cancelationToDos) {
                            logger.log(Level.SEVERE, prefix + "closing DDCTODO with id: " + cancelationToDo.getId());
                            cancelationToDo.setStopped(true);
                            debitCancelationToDoRepository.save(cancelationToDo);
                            logger.log(Level.SEVERE, prefix + "ddCToDo with id " + cancelationToDo.getId() + " cancelled successfully");
                        }
                    }
                }
            }
        }
        logger.log(Level.SEVERE, "ContractReScheduleTerminationBusinessRule execute end.");
        return map;
    }
}
