/*
Stopped in ACC-6668 ACC-7191
package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DDMessaging;
import com.magnamedia.entity.Payment;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.BouncingFlowService;
import com.magnamedia.service.DDMessagingService;
import com.magnamedia.service.SwitchingBankAccountService;
import org.joda.time.LocalDate;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

*/
/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 15, 2020
 *          ACC-1611
 *//*

@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterUpdate, BusinessEvent.AfterCreate},
        fields = {"id", "contract.id", "dateOfPayment",
                "methodOfPayment", "typeOfPayment.id", "amountOfPayment","directDebit.id",
                "status", "contractScheduleDateOfTermination", "msgBr2Checked"})

public class BouncedPaymentMessagesBR2 implements BusinessAction<Payment> {
    private static final Logger logger = Logger.getLogger(BouncedPaymentMessagesBR2.class.getName());

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "validation payment id " + entity.getId() +
                "; payment status " + entity.getStatus() +
                "; getMsgBr2Checked: " + entity.getMsgBr2Checked() +
                "; getMethodOfPayment: " + entity.getMethodOfPayment());

        if (entity.getMsgBr2Checked()) return false;
        
        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery<>(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);

        List<Payment> oldPayments = historyQuery.execute();

        Payment old = oldPayments.isEmpty() ? null : oldPayments.get(0);

        Contract contract = Setup.getRepository(ContractRepository.class)
                .findOne(entity.getContract().getId());

        logger.log(Level.SEVERE, "execute contract scheduleTermDate: " + contract.getScheduledDateOfTermination() +
                "; contractScheduleTermDate: " + entity.getContractScheduleDateOfTermination() +
                "; contract status: " + contract.getStatus() +
                "; payment Status: " + (old != null ? old.getStatus() : ""));

        if (Setup.getApplicationContext().getBean(SwitchingBankAccountService.class)
                .isClientSwitchingBankAccount(entity)) {
            logger.log(Level.SEVERE, "client is switching bank account");
            return false;
        }

        if (Setup.getApplicationContext().getBean(BouncingFlowService.class)
                .isBouncingFlowStopped(entity)) {
            logger.log(Level.SEVERE, "Bouncing Flow is Stopped");
            return false;
        }

        return entity.getMethodOfPayment() == PaymentMethod.CHEQUE
                && (contract.getStatus() == ContractStatus.CANCELLED || contract.getStatus() == ContractStatus.EXPIRED)
                && (!Setup.getRepository(ContractRepository.class).existsByStatusAndClient(ContractStatus.ACTIVE, contract.getClient()))
                && (old == null || old.getStatus() == null || !old.getStatus().equals(PaymentStatus.BOUNCED)) && entity.getStatus().equals(PaymentStatus.BOUNCED);
    }

    @Override
    public Map execute(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "execution starts");
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());

        DirectDebitMessagingScheduleTermCategory scheduleTermCategory = null;
        if (entity.getContractScheduleDateOfTermination() != null) {
            Date scheduledDateOfTermination = entity.getContractScheduleDateOfTermination();
            LocalDate localDate = new LocalDate(scheduledDateOfTermination);
            if (localDate.getYear() == new LocalDate().getYear()
                    && localDate.getDayOfYear() == new LocalDate().getDayOfYear()) {
                
                scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.EToday;
            } else {
                scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.GToday;
            }
        } else {
            scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.None;
        }

        logger.log(Level.SEVERE, "execute scheduleTermCategory selected: " + scheduleTermCategory);

        PicklistItem BPReceived = PicklistHelper.getItem(
                "BouncedPaymentStatus","pays_through_cheque");

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.BouncedPayment);
        query.filterBy("bouncedPaymentStatus", "=", BPReceived);
        query.filterBy("contractProspectTypes", "like", "%" + contract.getContractProspectType().getCode() + "%");
        query.setLimit(1);

        DDMessaging ddMessaging = DDMessagingService.getDdMessaging(query, contract, scheduleTermCategory);

        if (ddMessaging != null) {

            logger.info("execute message founded with id: " + ddMessaging.getId());
            Setup.getApplicationContext().getBean(DDMessagingService.class)
                    .applyDdMessaging(contract, ddMessaging, entity);
        } else {
            logger.info("execute message not founded");
        }

        logger.info("execution ends");

        Map<String, Object> map = new HashMap();
        map.put("msgBr2Checked", true);
        return map;
    }
}
*/
