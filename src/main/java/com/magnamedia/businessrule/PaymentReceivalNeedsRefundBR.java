package com.magnamedia.businessrule;


import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.ClientMessagingAndRefundService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jun 29, 2020
 *         Jirra ACC-2180
 */

// if payment.getToBeRefundedPartially() != null add refund for downgrade nationality
@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterUpdate},
        fields = {"id", "toBeRefundedPartially", "amountToRefundPartially", "contract.id", "status", "pRNRBrChecked"})
public class PaymentReceivalNeedsRefundBR implements BusinessAction<Payment> {
    
    private static final Logger logger = Logger.getLogger(PaymentReceivalNeedsRefundBR.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, prefix + "PaymentReceivalNeedsRefundBR Validation.");
        logger.log(Level.SEVERE, prefix + prefix + "payment id: " + (entity.isNewInstance() ? "new instance" : entity.getId()));
        
        if (entity.ispRNRBrChecked()) return false;
        if (entity.getContract() == null || entity.getContract().getId() == null) return false;
        if (entity.getStatus() == null) return false;

        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<Payment> oldPayments = historyQuery.execute();
        if (oldPayments == null || oldPayments.isEmpty()) return false;

        Payment old = oldPayments.get(0);
        logger.log(Level.SEVERE, prefix + "old payment found");

        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());
        logger.log(Level.SEVERE, prefix + "contract id: " + (contract != null ? contract.getId() : "contract is NULL"));

        return entity.getStatus().equals(PaymentStatus.RECEIVED) &&
                (old.getStatus() == null || !old.getStatus().equals(PaymentStatus.RECEIVED)) &&
                contract != null && entity.getToBeRefundedPartially() != null && entity.getToBeRefundedPartially();
    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        logger.log(Level.SEVERE, prefix + "PaymentReceivalNeedsRefundBR Execution.");

        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());
        logger.log(Level.SEVERE, prefix + "PaymentReceivalNeedsRefundBR refund started.");

        // Last record with flagged as toBeRefundedPartially create on 2021-04-13
        Setup.getApplicationContext().getBean(ClientMessagingAndRefundService.class).refundAfterReplacement(
                contract, entity.getAmountToRefundPartially(),
            AccountingModule.PAYMENT_REQUEST_PURPOSE_SWITCHING_TO_A_CHEAPER_NATIONALITY_REFUND, "Payment Received Event flow");
        logger.log(Level.SEVERE, prefix + "PaymentReceivalNeedsRefundBR refund is done.");

        Map map = new HashMap();
        map.put("pRNRBrChecked", Boolean.TRUE);
        return map;
    }
}
