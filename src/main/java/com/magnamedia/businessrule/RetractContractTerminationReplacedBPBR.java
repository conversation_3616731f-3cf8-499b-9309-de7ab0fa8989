package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Payment;
import com.magnamedia.service.BouncingFlowService;
import com.magnamedia.service.ContractService;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on May 18, 2020
 *         Jirra ACC-1915
 */

// retract contract termination if it canceled by bounced flow
@BusinessRule(entity = Payment.class, events = {BusinessEvent.BeforeUpdate},
        fields = {"id", "replaced", "contract.id", "status", "rCTRBPBrChecked"})
public class RetractContractTerminationReplacedBPBR implements BusinessAction<Payment> {

    private static final Logger logger = Logger.getLogger(RetractContractTerminationReplacedBPBR.class.getName());

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.info( "payment id: " + entity.getId());

        return !entity.isrCTRBPBrChecked() && BouncingFlowService.shouldRetractBouncingFlowTermination(entity);
    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        logger.info("payment id: " + entity.getId());
        Map map = new HashMap();
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .retractContractTermination(entity.getContract());

        map.put("rCTRBPBrChecked", Boolean.TRUE);

        return map;
    }
}
