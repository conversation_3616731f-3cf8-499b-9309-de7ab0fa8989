/*
package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.extra.NotificationUtils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import com.magnamedia.repository.ContractPaymentTermRepository;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

*/
/**
 *
 * <AUTHOR> <PERSON> <<EMAIL>>
 *//*


@BusinessRule(entity = ContractPaymentConfirmationToDo.class,
                events = {BusinessEvent.AfterCreate, BusinessEvent.BeforeUpdate},
                fields = {"id", "contractPaymentTerm.id", "paymentMethod", "showOnERP", "notificationSent"})
public class ContractPaymentNotificationBusinessRule implements BusinessAction<ContractPaymentConfirmationToDo>{
    
    private static final Logger logger =
            Logger.getLogger(ContractPaymentNotificationBusinessRule.class.getName());
    
    @Override
    public boolean validate(ContractPaymentConfirmationToDo contractPaymentConfirmationToDo, BusinessEvent businessEvent) {
        logger.log(Level.SEVERE, "ContractPaymentNotificationBusinessRule Validation.");
        if (contractPaymentConfirmationToDo.isNotificationSent())
            return false;
        if (!contractPaymentConfirmationToDo.isShowOnERP())
            return false;
        //Jira ACC-990
        if (contractPaymentConfirmationToDo.getPaymentMethod() == null
                || contractPaymentConfirmationToDo.getPaymentMethod().equals(PaymentMethod.DIRECT_DEBIT))
            return false;
        if (businessEvent.equals(BusinessEvent.AfterUpdate)){
            ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository =
                    Setup.getRepository(ContractPaymentConfirmationToDoRepository.class);
            if (contractPaymentConfirmationToDo.getId()!=null){
                ContractPaymentConfirmationToDo old = contractPaymentConfirmationToDoRepository.findOne(contractPaymentConfirmationToDo.getId());
                if (old.isShowOnERP())
                    return false;
            }
            else
                return false;
                
        }
        return true;
    }
    
    @Override
    public Map<String, Object> execute(ContractPaymentConfirmationToDo contractPaymentConfirmationToDo, BusinessEvent businessEvent) {

        logger.log(Level.SEVERE, "ContractPaymentNotificationBusinessRule execute.");
        ContractPaymentTermRepository contractPaymentTermRepository =
                Setup.getRepository(ContractPaymentTermRepository.class);
        ContractPaymentTerm c = contractPaymentTermRepository.findOne(
                contractPaymentConfirmationToDo.getContractPaymentTerm().getId());
        String clientName = "";
        if (c != null && c.getContract() != null
                && c.getContract().getClient() != null)
            clientName = c.getContract().getClient().getName();
        String title = "New payment confirmation for " + clientName;
        String body = "A new payment confirmation for " + clientName + "<br/>"
                + "Please refer to (<a href='#!/accounting/payments-automation/contract-payments'>Contract Payments Confirmation</a>)<br/>";
        
        NotificationUtils notificationUtils =
                Setup.getApplicationContext().getBean(NotificationUtils.class);
        notificationUtils.sendNotificationOfType(title, body, AccountingModule.NEW_CONTRACT_PAYMENT_NOTIFICATION_TYPE);
        logger.log(Level.SEVERE, "ContractPaymentNotificationBusinessRule execute end.");
        Map map = new HashMap();
        map.put("notificationSent", true);
        return map;
    }
}
*/
