package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.BasePLVariableBucket;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.Revenue;
import com.magnamedia.entity.interfaces.BasePLVariableNode;
import com.magnamedia.entity.projection.BasePlVariableBucketProjection;
import com.magnamedia.extra.PLVariableBucketEntity;
import com.magnamedia.repository.BasePLVariableBucketRepository;
import com.magnamedia.repository.BasePLVariableRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Masod <<EMAIL>>
 * Created on Jul 20, 2020
 * Jirra ACC-644
 */

public class BasePLVariableBucketController<T extends BasePLVariableBucket> extends BaseRepositoryController<T> {

    @Autowired
    private ProjectionFactory projectionFactory;

    @PreAuthorize("hasPermission('plvariablebuckets','searchbyexpense')")
    @RequestMapping(value = "/searchbyexpense/{id}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> searchByExpense(@PathVariable("id") Expense expense) {

        if (expense != null) {
            List<BasePLVariableBucket> pLVariableBuckets = getRepository().findByExpense(expense)
                    .stream().map(t -> ((BasePLVariableBucket) t)).collect(Collectors.toList());
            return new ResponseEntity<>(
                    pLVariableBuckets.stream().map(x -> new PLVariableBucketEntity(x)).collect(Collectors.toList()),
                    HttpStatus.OK);
        } else
            return new ResponseEntity<>("the expense Could not be found.", HttpStatus.BAD_REQUEST);

    }

    @PreAuthorize("hasPermission('plvariablebuckets','searchbyrevenue')")
    @RequestMapping(value = "/searchbyrevenue/{id}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> searchByRevenue(@PathVariable("id") Revenue revenue) {

        if (revenue != null) {
            List<BasePLVariableBucket> pLVariableBuckets = getRepository().findByRevenue(revenue)
                    .stream().map(t -> ((BasePLVariableBucket) t)).collect(Collectors.toList());
            return new ResponseEntity<>(
                    pLVariableBuckets.stream().map(x -> new PLVariableBucketEntity(x)).collect(Collectors.toList()),
                    HttpStatus.OK);
        } else
            return new ResponseEntity<>("the revenue Could not be found.", HttpStatus.BAD_REQUEST);
    }

    //Jirra ACC-280
    @PreAuthorize("hasPermission('plvariablebuckets','searchexpenseandrevenue')")
    @RequestMapping(value = "/searchexpenseandrevenue", method = RequestMethod.GET)
    public ResponseEntity<?> searchExpenseAndRevenue(
            @RequestParam(value = "search", required = false) String queryString) {

        SelectQuery<Expense> query = new SelectQuery<>(Expense.class);
        if (queryString != null && !queryString.isEmpty()) {
            query.filterBy(
                    new SelectFilter("name", "like", "%" + queryString + "%")
                            .or("code", "like", "%" + queryString + "%"));
        }
        query.filterBy("deleted", "=", false);

        query.sortBy("code", true, false);

        SelectQuery<Revenue> query2 = new SelectQuery<>(Revenue.class);
        if (queryString != null && !queryString.isEmpty()) {
            query2.filterBy(
                    new SelectFilter("name", "like", "%" + queryString + "%")
                            .or("code", "like", "%" + queryString + "%"));
        }

        query2.sortBy("code", true);

        List<revexpProjection> result =
                query.execute().stream()
                        .map(x -> new revexpProjection(x))
                        .collect(Collectors.toList());
        result.addAll(query2.execute().stream()
                .map(x -> new revexpProjection(x))
                .collect(Collectors.toList()));

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    //Jirra ACC-2395
    @PreAuthorize("hasPermission('plvariablebuckets','searchexpenseandrevenue')")
    @RequestMapping(value = "/getbucketUsage", method = RequestMethod.POST)
    public ResponseEntity getBucketsUsage(@RequestBody List<Long> variableIDs) {
        List<BasePLVariableNode> variableNodes = (List<BasePLVariableNode>) getPlVariableRepository().findAll(variableIDs);
        List<BasePLVariableBucket> relatedVariableBuckets = new ArrayList();

        for (BasePLVariableNode variableNode : variableNodes)
            for (BasePLVariableBucket variableBucket : (List<BasePLVariableBucket>) variableNode.getpLVariableBuckets()) {
                if (variableBucket.getExpense() != null) {
                    relatedVariableBuckets.addAll(getRepository().findByExpenseAndIdNotIn(variableBucket.getExpense(), Arrays.asList(variableBucket.getId())));
                }
                if (variableBucket.getRevenue() != null) {
                    relatedVariableBuckets.addAll(getRepository().findByRevenueAndIdNotIn(variableBucket.getRevenue(), Arrays.asList(variableBucket.getId())));
                }
            }

        return new ResponseEntity(
                relatedVariableBuckets.stream().filter(variableBucket -> variableBucket.getpLVariable().getParent().getParent()
                        .getPLCompany().getCompany().isIsActive()).map(
                        obj -> projectionFactory.createProjection(
                                BasePlVariableBucketProjection.class, obj))
                        .collect(Collectors.toList()),
                HttpStatus.OK);
    }

    class revexpProjection {
        private final long id;
        private final String name;
        private final String code;
        private final String bucketType;

        public revexpProjection(Expense e) {
            this.id = e.getId();
            this.name = e.getName();
            this.code = e.getCode();
            this.bucketType = "Expense";
        }

        public revexpProjection(Revenue e) {
            this.id = e.getId();
            this.name = e.getName();
            this.code = e.getCode();
            this.bucketType = "Revenue";
        }

        public long getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public String getCode() {
            return code;
        }

        public String getBucketType() {
            return bucketType;
        }

    }

    public BasePLVariableRepository getPlVariableRepository() {
        return null;
    }

    @Override
    public BasePLVariableBucketRepository<T> getRepository() {
        return null;
    }
}

