package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.entity.*;
import com.magnamedia.extra.UploadStatementEntityType;
import com.magnamedia.extra.VisaStatementTransactionType;
import com.magnamedia.extra.VisaStatementType;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.TransactionEntityType;
import com.magnamedia.repository.*;
import com.magnamedia.service.VisaExpenseService;
import com.magnamedia.service.VisaStatementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RequestMapping("/visaStatementTransaction")
@RestController
public class VisaStatementTransactionController extends BaseRepositoryController<VisaStatementTransaction> {

    @Autowired
    private VisaStatementTransactionRepository visaStatementTransactionRepository;
    @Autowired
    private VisaStatementRepository visaStatementRepository;
    @Autowired
    private VisaExpenseService expenseService;
    @Autowired
    private VisaStatementService visaStatementService;
    @Autowired
    private BackgroundTaskHelper backgroundTaskHelper;

    @Override
    public BaseRepositoryParent<VisaStatementTransaction> getRepository() {
        return visaStatementTransactionRepository;
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','getAllTransaction')")
    @RequestMapping(value = "/getAllTransaction/{id}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> getAllTransaction(@PathVariable("id") VisaStatement visaStatement) {
        SelectQuery query = new SelectQuery(VisaStatementTransaction.class);
        query.filterBy("statement", "=", visaStatement);
        query.sortBy("type", false);
        return new ResponseEntity<>(query.execute(), HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('visaStatementTransaction','getMatchedTransaction')")
    @RequestMapping(value = "/getMatchedTransaction/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getMatchedTransaction(@PathVariable("id") VisaStatement visaStatement, Pageable page) {
        SelectQuery query = new SelectQuery(VisaStatementTransaction.class);
        query.filterBy("statement", "=", visaStatement);
        query.filterBy("type", "=", VisaStatementTransactionType.Matched);

        return new ResponseEntity<>(query.execute(page), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','getMissingFromERPTransaction')")
    @RequestMapping(value = "/getMissingFromERPTransaction/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getMissingFromERPTransaction(@PathVariable("id") VisaStatement visaStatement, Pageable page) {
        SelectQuery query = new SelectQuery(VisaStatementTransaction.class);
        query.filterBy("statement", "=", visaStatement);
        query.filterBy("type", "=", VisaStatementTransactionType.MissingFromERP);

        return new ResponseEntity<>(query.execute(page), HttpStatus.OK);
    }

    /*@PreAuthorize("hasPermission('visaStatementTransaction','getMissingFromStatementTransaction')")
    @RequestMapping(value = "/getMissingFromStatementTransaction/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getMissingFromStatementTransaction(@PathVariable("id") VisaStatement visaStatement, Pageable page) {

        Page<Object[]> r = Setup.getRepository(NewVisaRequestExpenseRepository.class)
                .getVisaExpenseMissingFromStatement(
                        visaStatement.getId(), visaStatement.getStart(),
                        new DateTime(visaStatement.getEnd()).withTimeAtStartOfDay().plusDays(1).minusMillis(1).toDate(),
                        page);
        PicklistItem maidVisa = PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE,
                "maidvisa.ae_prospect");

        List<VisaStatementTransaction> l = r.getContent()
                .stream()
                .map(x -> new VisaStatementTransaction(x, visaStatement, null, maidVisa))
                .collect(Collectors.toList());

        return ResponseEntity.ok(new AccountingPage(l, page, r.getTotalElements(), null));
    }*/

    @PreAuthorize("hasPermission('visaStatementTransaction','getSameReferenceNumberButDifferentAmountTransaction')")
    @GetMapping(value = "/getSameReferenceNumberButDifferentAmountTransaction/{id}")
    public ResponseEntity<?> getSameReferenceNumberButDifferentAmountTransaction(@PathVariable("id") VisaStatement visaStatement, Pageable page) {
        SelectQuery query = new SelectQuery(VisaStatementTransaction.class);
        query.filterBy("statement", "=", visaStatement);
        query.filterBy("type", "=", VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);

        return new ResponseEntity<>(query.execute(page), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','dismiss')")
    @PostMapping(value = "/dismiss/{id}")
    @Transactional
    public ResponseEntity<?> dismissVisaRequestExpenseList(
            @PathVariable("id") VisaStatement visaStatement,
            @RequestBody List<Map<String, Object>> visaExpenses) {

        visaExpenses.forEach(v -> expenseService.dismissVisaRequest((String) v.get("type"), Long.valueOf(v.get("id").toString())));
        visaStatementService.refreshStatement(visaStatement);

        return ResponseEntity.ok("Done.");
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','wrongMatch')")
    @GetMapping(value = "/wrongMatch/{id}")
    @Transactional
    public ResponseEntity<?> wrongMatch(@PathVariable("id") VisaStatementTransaction statementTransaction) {
        if(BackgroundTaskHelper.checkIfFoundAnyBGT(UploadStatementEntityType.ConfirmVisaStatementTransaction,
                        statementTransaction.getStatement().getId(), "VisaStatement")) {

            throw new RuntimeException("This action is disabled for Amwal statements. " +
                    "Auto-confirmation of transactions is in progress.");
        }

        statementTransaction.setType(VisaStatementTransactionType.MissingFromERP);
        statementTransaction.initObj();
        visaStatementTransactionRepository.save(statementTransaction);

        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','rematchAll')")
    @GetMapping(value = "/rematchAll/{id}")
    public ResponseEntity<?> rematchAll(@PathVariable("id") VisaStatement v) {

        List<VisaStatementTransaction> l = visaStatementTransactionRepository.findByStatementAndTypeAndFinishedFalse(
                v, VisaStatementTransactionType.MissingFromERP);
        if (l.isEmpty()) return okResponse();
        l.forEach(t -> rematch(t));

        return okResponse();
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','rematch')")
    @PostMapping(value = "/rematch")
    @Transactional
    public ResponseEntity<?> rematch(@RequestBody List<Long> ids) {
        for (Long id : ids) {
            rematch(visaStatementTransactionRepository.findOne(id));
        }

        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    private void rematch(VisaStatementTransaction t) {
        List<Object[]> visaExpense = Setup.getRepository(NewVisaRequestExpenseRepository.class).
                findByExpenseReferenceNumber(t.getReferenceNumber());
        if (visaExpense.isEmpty()) return;

        PicklistItem maidVisa = PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE,
                "maidvisa.ae_prospect");

        t.fillVisaStatementTransactionInfo(visaExpense.get(0), t.getStatement(), t.getAmount(), maidVisa);
        t.setType(((Double)t.getAmount()).equals((visaStatementService.getErpFullAmount(visaExpense.get(0)))) ?
                VisaStatementTransactionType.Matched :
                VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);

        visaStatementTransactionRepository.save(t);
        visaStatementService.refreshStatement(t.getStatement());

        // auto confirm
        if (t.getType().equals(VisaStatementTransactionType.Matched) &&
                t.getDescription() != null && !t.getDescription().isEmpty() &&
                t.getFromBucket() != null && t.getExpense() != null) {

            visaStatementService.confirmTransaction(t.getId());
        }
        }

    @PreAuthorize("hasPermission('visaStatementTransaction','confirmAll')")
    @GetMapping(value = "/confirmAll/{id}")
    public ResponseEntity<?> confirmAll(@PathVariable("id") VisaStatement visaStatement){
        if(BackgroundTaskHelper.checkIfFoundAnyBGT(UploadStatementEntityType.ConfirmVisaStatementTransaction,
                        visaStatement.getId(), "VisaStatement")) {

            throw new RuntimeException("This action is disabled for Amwal statements. " +
                    "Auto-confirmation of transactions is in progress.");
        }

        backgroundTaskHelper.createBGTPreConfirmAllVisaStatementRecords(visaStatement.getId());
        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','confirm')")
    @PostMapping(value = "/confirm/{id}")
    @Transactional
    public ResponseEntity<?> confirm(
            @PathVariable("id") VisaStatementTransaction visaStatementTransaction,
            @RequestBody Transaction transaction) {

        if(BackgroundTaskHelper.checkIfFoundAnyBGT(UploadStatementEntityType.ConfirmVisaStatementTransaction,
                        visaStatementTransaction.getStatement().getId(), "VisaStatement")) {

            throw new RuntimeException("This action is disabled for Amwal statements. " +
                    "Auto-confirmation of transactions is in progress.");
        }

        return visaStatementService.confirm(visaStatementTransaction, transaction);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','confirmSelected')")
    @PostMapping(value = "/confirmSelected")
    @Transactional
    public ResponseEntity<?> confirmSelected(@RequestBody Map<Long, Transaction> body) {
        Set<Long> ids = body.keySet();
        List<VisaStatementTransaction> l = visaStatementTransactionRepository.findAllById(ids);
        if (l.isEmpty()) {
            throw new BusinessException("There are no transactions to be confirmed");
        }

        if(BackgroundTaskHelper.checkIfFoundAnyBGT(UploadStatementEntityType.ConfirmVisaStatementTransaction,
                        l.get(0).getStatement().getId(), "VisaStatement")) {

            throw new RuntimeException("This action is disabled for Amwal statements. " +
                    "Auto-confirmation of transactions is in progress.");
        }

        for (VisaStatementTransaction t : l) {
            Transaction transaction = body.get(t.getId());
            confirm(t, transaction);
        }

        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','fixSelectedERPAmount')")
    @PostMapping(value = "/fixSelectedERPAmount")
    @Transactional
    public ResponseEntity<?> fixSelectedERPAmount(@RequestBody List<Long> ids) {
        for (Long id : ids) {
            VisaStatementTransaction t = visaStatementTransactionRepository.findOne(id);
            double newAmount = t.getAmount();

            VisaExpense expense = expenseService.getVisaExpenseByType(
                    t.getVisaExpenseType(), t.getVisaRequestExpenseID());
            expense.setAmount(newAmount);
            expense.setCharge(null);
            expense.setVatCharge(null);
            expenseService.saveVisaExpenseByType(t.getVisaExpenseType(), expense);

            t.setType(VisaStatementTransactionType.Matched);

            // Update description to use correct date when transaction becomes matched
            if (t.getVisaRequestExpenseID() != null) {
                t.setDescriptionByDateAndReferenceNumber(
                        t.getStatement().getType().equals(VisaStatementType.Noqodi) && t.getRowRecordDate() != null
                                ? t.getRowRecordDate()
                                : t.getExpenseCreationDate(),
                        t.getStatement().getType().equals(VisaStatementType.Noqodi) &&
                                t.isCredit() && t.getRefundReferenceNumber() != null
                                ? t.getRefundReferenceNumber()
                                : t.getReferenceNumber());
            }

            t = visaStatementTransactionRepository.save(t);

            if (t.getDescription() != null &&
                    !t.getDescription().isEmpty() &&
                    t.getFromBucket() != null &&
                    t.getExpense() != null) {

                visaStatementService.confirmTransaction(t.getId());
            }

            visaStatementService.refreshStatement(t.getStatement());
        }

        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatementTransaction','acc8358FixTransactions')")
    @GetMapping(value = "/acc8358FixTransactions")
    @Transactional
    public ResponseEntity<?> acc8358FixTransactions() {
        TransactionRepository transactionRepository = Setup.getRepository(TransactionRepository.class);
        HousemaidTransactionRepository housemaidTransactionRepository = Setup.getRepository(HousemaidTransactionRepository.class);

        SelectQuery<VisaStatementTransaction> q = new SelectQuery<>(VisaStatementTransaction.class);
        q.filterBy("officeStaff", "is not null", null);
        q.filterBy("transaction.transactionType", "=", TransactionEntityType.HOUSEMAID);

        List<VisaStatementTransaction> l = q.execute();
        for (VisaStatementTransaction v : l) {
            Transaction t = v.getTransaction();
            t.setTransactionType(TransactionEntityType.OFFICE_STAFF);
            OfficeStaffTransaction o = new OfficeStaffTransaction();
            o.setTransaction(t);
            o.setOfficeStaff(v.getOfficeStaff());
            t.setOfficeStaffs(Collections.singletonList(o));
            t.setPassInitLists(true);
            transactionRepository.save(t);

            housemaidTransactionRepository.findByTransaction(t)
                    .forEach(housemaidTransactionRepository::delete);
        }

        return ResponseEntity.ok("done");
    }
}
