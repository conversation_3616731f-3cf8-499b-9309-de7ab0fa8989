package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankDirectDebitActivationRecord;
import com.magnamedia.repository.BankDirectDebitActivationRecordRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 17, 2018
 * Jirra ACC-423
 */
@RequestMapping("/bddactivationrecords")
@RestController
public class BankDirectDebitActivationRecordController
        extends BaseRepositoryController<BankDirectDebitActivationRecord>{

    @Autowired
    private BankDirectDebitActivationRecordRepository bankDirectDebitActivationRecordRepository;
    
    @Override
    public BaseRepository<BankDirectDebitActivationRecord> getRepository() {
        return bankDirectDebitActivationRecordRepository;
    }
    
}
