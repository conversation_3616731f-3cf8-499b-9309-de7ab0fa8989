package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.CooQuestion;
import com.magnamedia.entity.workflow.CooNightReviewTodo;
import com.magnamedia.repository.CooNightReviewTodoRepository;
import com.magnamedia.repository.CooQuestionRepository;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/cooNightReviewTodo")
public class CooNightReviewTodoController extends BaseRepositoryController<CooNightReviewTodo> {
    @Autowired
    private CooNightReviewTodoRepository cooNightReviewTodoRepository;

    @Override
    public BaseRepository<CooNightReviewTodo> getRepository() {
       return Setup.getRepository(CooNightReviewTodoRepository.class);
    }

    @NoPermission
    @GetMapping(value = "/getCooNightReview")
    public ResponseEntity getCooNightReview(
            @RequestParam(value = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(value = "archive") boolean archive,
            @RequestParam CooNightReviewTodo.CooNightReviewType type,
            Pageable pageable) {

        if (!archive)
            return new ResponseEntity(cooNightReviewTodoRepository.findByTypeAndDoneByCooFalse(type, pageable)
                    .map(this::convertToCooNightReviewDto), HttpStatus.OK);

        return new ResponseEntity(cooNightReviewTodoRepository.findByTypeAndCreationDate(
                        type,
                        new DateTime(startDate).minusDays(30).withTimeAtStartOfDay().toDate(),
                        new DateTime(startDate).toDate(), pageable)
                .map(this::convertToCooNightReviewDto), HttpStatus.OK);
    }

    private Map<String, Object> convertToCooNightReviewDto(CooNightReviewTodo t) {
        List<CooQuestion> cooQuestions = Setup.getRepository(CooQuestionRepository.class)
                .findByRelatedEntityIdAndRelatedEntityTypeAndQuestionedPage(
                        t.getId(), "cooNightReviewTodo",
                        CooQuestion.QuestionedPage.NIGHT_REVIEW);

        Map<String, Object> output = new HashMap<>();
        output.put("id", t.getId());
        if (t.getType().equals(CooNightReviewTodo.CooNightReviewType.MAIDS_LOANS_WAIVER)) {
            output.put("maidName", t.getMaidName());
            output.put("maidJoiningDate", t.getMaidJoiningDate());
            output.put("maidSalary", t.getMaidSalary());
            output.put("loanWaiverAmount", t.getLoanWaiverAmount());
            output.put("cooNightReviewNote", t.getCooNightReviewNote());
            output.put("maidNationality", t.getMaidNationality() == null ?
                    "" : t.getMaidNationality().getName());
            output.put("maidType", t.getMaidType() == null ?
                    "" : t.getMaidType().getName());
            output.put("requester", t.getRequester() == null ?
                    "" : t.getRequester().getFullName());
            output.put("approvedBy", t.getApprovedBy() == null ?
                    "" : t.getApprovedBy().getFullName());
        } else if (t.getType().equals(CooNightReviewTodo.CooNightReviewType.REVIEW_DISCOUNT)) {
            output.put("clientName", t.getClient().getName());
            output.put("contractType", t.getContractProspectType().getCode().equals("maidvisa.ae_prospect") ? "MV" : "CC");
            output.put("discountPlan", t.getPaymentPlan() == null ?
                    "" : t.getPaymentPlan().getName());
            output.put("discount", t.getDiscount());
            output.put("discountMonths", t.getDiscountMonths());
            output.put("discountReason", t.getDiscountReason() == null ?
                    "" : t.getDiscountReason().getName());
            output.put("discountCaseDescription", t.getDiscountDescription() == null ?
                    "" : t.getDiscountDescription());
            output.put("discountUser", t.getDiscountUser() == null ?
                    "" : t.getDiscountUser().getName());
            output.put("ptcReason", t.getPtcReason() == null ?
                    "" : t.getPtcReason());
            output.put("ptcDescription", t.getPtcDescription() == null ?
                    "" : t.getPtcDescription());
            output.put("ptcUser", t.getPtcUser() == null ?
                    "" : t.getPtcUser());
        }

        boolean allQuestionsAnswered = false, oneQuestionAnswered = false, noneQuestionAnswered = false;
        if (!cooQuestions.isEmpty()) {
            allQuestionsAnswered = cooQuestions.stream().allMatch(CooQuestion::isAnswered);
            oneQuestionAnswered = cooQuestions.stream().anyMatch(CooQuestion::isAnswered);
            noneQuestionAnswered = cooQuestions.stream().noneMatch(CooQuestion::isAnswered);
        }

        output.put("cooQuestions", cooQuestions);
        output.put("allQuestionsAnswered", allQuestionsAnswered);
        output.put("oneQuestionAnswered", oneQuestionAnswered);
        output.put("noneQuestionAnswered", noneQuestionAnswered);

        return output;
    }

    @PreAuthorize("hasPermission('cooNightReviewTodo','mark-all-as-done-by-coo')")
    @GetMapping(value = "/mark-all-as-done-by-coo")
    public ResponseEntity markAllAsDoneByCooAPI(
            CooNightReviewTodo.CooNightReviewType type,
            Pageable pageable) {

        cooNightReviewTodoRepository.findByTypeAndDoneByCooFalse(type, pageable).getContent()
                .forEach(o -> markAsDoneByCoo(o.getId()));

        return okResponse();
    }

    @PreAuthorize("hasPermission('cooNightReviewTodo','mark-list-as-done-by-coo')")
    @RequestMapping(value = "/mark-list-as-done-by-coo", method = RequestMethod.POST)
    public ResponseEntity markListAsDoneByCooAPI(@RequestBody List<Long> ids) {
        ids.forEach(this::markAsDoneByCoo);
        return okResponse();
    }

    public void markAsDoneByCoo(Long id) {
        CooNightReviewTodo cooNightReviewTodo = cooNightReviewTodoRepository.findOne(id);
        cooNightReviewTodo.setDoneByCoo(true);
        cooNightReviewTodoRepository.save(cooNightReviewTodo);
    }
}
