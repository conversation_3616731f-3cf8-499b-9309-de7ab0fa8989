package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.BankDirectDebitCancelationFile;
import com.magnamedia.entity.BankDirectDebitCancelationRecord;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.repository.BankDirectDebitCancelationRecordRepository;
import com.magnamedia.service.BankDirectDebitCancellationRecordService;
import com.magnamedia.service.DirectDebitCancellationService;
import com.magnamedia.service.QueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 20, 2019
 * Jirra ACC-1134
 */
@RequestMapping("/bddcancelationrecords")
@RestController
public class BankDirectDebitCancelationRecordController
        extends BaseRepositoryController<BankDirectDebitCancelationRecord>{

    @Autowired
    private BankDirectDebitCancelationRecordRepository bankDirectDebitCancelationRecordRepository;
    @Autowired
    private BankDirectDebitCancellationRecordService bankDirectDebitCancellationRecordService;
    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;

    @Override
    public BaseRepository<BankDirectDebitCancelationRecord> getRepository() {
        return bankDirectDebitCancelationRecordRepository;
    }
    
    @PreAuthorize("hasPermission('bddcancelationrecords','bulkdelete')")
    @RequestMapping(value = "/bulkdelete", method = RequestMethod.POST)
    @ResponseBody
    @Transactional
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> bulkDelete(
            @RequestBody List<Long> ids) {
        
        List<BankDirectDebitCancelationRecord> records =
                bankDirectDebitCancelationRecordRepository.findAll(ids);
        for (BankDirectDebitCancelationRecord r : records){
            super.delete(r);
        }
        return new ResponseEntity<>(
                records.stream().map(x->x.getId()).collect(Collectors.toList()),
                HttpStatus.OK);
    }

    // ACC-9005
    @Transactional
    @PreAuthorize("hasPermission('bddcancelationfiles','approveAllRejectionByBankForDDs')")
    @PostMapping(value = "/approveAllRejectionByBankForDDs")
    public ResponseEntity<?> approveAllRejectionByBankForDDs(@RequestBody BankDirectDebitCancelationFile file) {
        List<Long> ids = bankDirectDebitCancelationRecordRepository.findIdsByBankDirectDebitCancellationFileId(file.getId());
        if (ids.isEmpty()) throw new BusinessException("there isn't any records to handle");

        return approveRejectionByBankForDDs(ids, false);
    }

    // ACC-9005
    @ResponseBody
    @Transactional
    @PreAuthorize("hasPermission('bddcancelationrecords','approveRejectionByBankForDDs')")
    @PostMapping("/approveRejectionByBankForDDs")
    public ResponseEntity<?> approveRejectionByBankForDDs(
            @RequestBody List<Long> ids,
            @RequestParam(value = "fromRPA", defaultValue = "false") boolean fromRPA) {
        if (ids == null || ids.isEmpty()) return badRequestResponse();

        if (QueryService.existsEntity(BackgroundTask.class, "e.name = :p0 and e.status not in :p1",
                new Object[]{"ApproveRejectBankDirectDebitCancellationFile",
                        Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed)})) {
            throw new BusinessException("This feature is requested before and still processing.");
        }

        List<BankDirectDebitCancelationRecord> records = bankDirectDebitCancelationRecordRepository.findAll(ids);

        BackgroundTaskHelper backgroundTaskHelper = Setup.getApplicationContext().getBean(BackgroundTaskHelper.class);
        Long userId = CurrentRequest.getUser().getId();
        StringBuilder error = new StringBuilder();
        for (BankDirectDebitCancelationRecord r : records) {
            if (r.getStatus().equals(BankDirectDebitCancelationRecord.status.CONFIRMED)) continue;

            boolean proceed = r.getDirectDebitFile() == null || directDebitCancellationService
                    .validateDirectDebitCancellation(r.getDirectDebitFile());
            if(!proceed) {
                error.append(r.getDirectDebitFile().getApplicationId()).append(", ");
                continue;
            }
            backgroundTaskHelper.createBGTForApprovalOnRejectionBank(
                    new HashMap<String, Object>() {{
                        put("id", r.getId());
                        put("status", r.getStatus());
                        put("fileId", r.getBankDirectDebitCancelationFile().getId());
                    }},
                    fromRPA,
                    userId);
        }

        if(error.length() > 0) {
            throw new BusinessException("DD '" + error + "' are under RPA process, they cannot be cancelled");
        }

        return ResponseEntity.ok("confirmation requested, dd will be updated.");
    }
}
