package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.PaymentMatchingRecord;
import com.magnamedia.entity.PaymentsMatchingFile;
import com.magnamedia.entity.projection.PaymentMatchingFileProjection;
import com.magnamedia.entity.projection.PaymentMatchingRecordProjection;
import com.magnamedia.entity.projection.PaymentMatchingRecordProjectionCSV;
import com.magnamedia.extra.AccountingPage;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.type.PaymentMatchingRecordStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.PaymentMatchingRecordRepository;
import com.magnamedia.repository.PaymentsMatchingFileRepository;
import com.magnamedia.service.PaymentService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Mar 13, 2019
 *         Jirra ACC-469
 */
@RequestMapping("/paymentsmatchingfiles")
@RestController
public class PaymentsMatchingFileController
        extends BaseRepositoryController<PaymentsMatchingFile> {

    private static final Logger logger =
            Logger.getLogger(BaseRepositoryController.class.getName());
    private static final String prefix = "MMM ";

    public static final String[] csvColumnTitles = new String[]{
            "Payment Id", "Client Name", "Contract Id", "Amount", "Previous Status",
            "Payment Status", "Imported Status", "Reason of Bounced Cheque", "Transferring Failure Reason"};

    public static final String[] csvColumn = new String[]{
            "paymentId", "clientName", "contractId", "paymentAmount", "prevStatus",
            "paymentStatus", "importedStatus", "reasonOfBouncedCheque", "transferringFailureReason"};

    @Autowired
    private PaymentsMatchingFileRepository paymentsMatchingFileRepository;

    @Autowired
    private PaymentMatchingRecordRepository paymentMatchingRecordRepository;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Autowired
    @Qualifier("taskExecutor")
    private TaskExecutor taskExecutor;

    @Autowired
    private BackgroundTaskService backgroundTaskService;

    //Jirra ACC-1870
    @Autowired
    private PaymentsMatchingFileController selfController;

    @Override
    public BaseRepository<PaymentsMatchingFile> getRepository() {
        return paymentsMatchingFileRepository;
    }

    @PreAuthorize("hasPermission('paymentsmatchingfiles','list')")
    @RequestMapping(value = "/projectedpage", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> projectedPage(
            Pageable pageable) {

        SelectQuery<PaymentsMatchingFile> query =
                new SelectQuery<>(PaymentsMatchingFile.class);
        query.filterBy("hidden", "=", false);
        query.sortBy("creationDate", false, true);

        return new ResponseEntity<>(
                query.execute(pageable).map(
                        obj -> projectionFactory.createProjection(
                                PaymentMatchingFileProjection.class, obj)),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('paymentsmatchingfiles','list')")
    @RequestMapping(value = "/projectedlist", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> projectedList() {

        SelectQuery<PaymentsMatchingFile> query =
                new SelectQuery<>(PaymentsMatchingFile.class);
        query.filterBy("hidden", "=", false);
        query.sortBy("creationDate", false, true);

        return new ResponseEntity<>(
                query.execute().stream().map(
                        obj -> projectionFactory.createProjection(
                                PaymentMatchingFileProjection.class, obj))
                        .collect(Collectors.toList()),
                HttpStatus.OK);
    }

    // Jirra ACC-769
//    @PreAuthorize("hasPermission('paymentsmatchingfiles','getrecords')")
//    @RequestMapping(value = "/getrecords/{id}", method = RequestMethod.GET)
//    @ResponseBody
//    @JsonView(ViewScope.Normal.class)
//    public ResponseEntity<?> getRecords(
//            @PathVariable(value = "id") Long id,
//            @RequestParam(name = "matched", required = false) boolean matched,
//            @RequestParam(name = "applied", required = false) boolean applied,
//            Pageable pageable) {
//
//        PaymentsMatchingFile file = getRepository().findOne(id);
//
//        List<PaymentMatchingRecord> records = new ArrayList<>();
//        Double balanceSum = 0D;
//        if (file != null) {
//            if (!matched) {
//                records = file.getNotMatchedRecords();
//                balanceSum = file.getNotMatchedRecordsAmount();
//            } else if (applied) {
//                records = file.getConfirmedRecords();
//                balanceSum = file.getConfirmedRecordsAmount();
//            } else {
//                records = file.getMatchedRecords();
//                balanceSum = file.getMatchedRecordsAmount();
//            }
//        }
//
//        PageImpl s = (PageImpl) PaginationUtil.listToPage(pageable, records);
//        AccountingPage accountingPageResult =
//                new AccountingPage(
//                        s.getContent(), pageable, s.getTotalElements(), balanceSum);
//        return new ResponseEntity<>(accountingPageResult, HttpStatus.OK);
//    }

    // Jirra ACC-769
//    @PreAuthorize("hasPermission('paymentsmatchingfiles','getrecords/csv')")
//    @RequestMapping(value = "/getrecords/csv/{id}", method = RequestMethod.GET)
//    public void getRecordsAsCSV(
//            @PathVariable(value = "id") Long id,
//            @RequestParam(name = "matched", required = false) boolean matched,
//            @RequestParam(name = "applied", required = false) boolean applied,
//            HttpServletResponse response) throws Exception {
//
//        PaymentsMatchingFile file = getRepository().findOne(id);
//
//        File csvFile = null;
//        PaymentMatchingRecordStatus recordStatus;
//        if (file != null) {
//            if (!matched) {
//                recordStatus = PaymentMatchingRecordStatus.NOT_MATCHED;
//            } else {
//                recordStatus = PaymentMatchingRecordStatus.CONFIRMED;
//            }
//
//            List<PaymentMatchingRecord> records = getRecordsByStatusForCSV(id, recordStatus);
//            csvFile = CsvHelper.generateCsv(records, PaymentMatchingRecordProjectionCSV.class, csvColumnTitles, csvColumn, "Payments Details");
//        }
//
//        createDownloadResponse(response,
//                "Payments Details.csv",
//                new FileInputStream(csvFile));
//    }

    // Jirra ACC-1870
    @PreAuthorize("hasPermission('paymentsmatchingfiles','getrecords/by-status')")
    @RequestMapping(value = "/getrecords/by-status/{id}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> getRecordsByStatus(
            @PathVariable(value = "id") Long id,
            @RequestParam(name = "status") PaymentMatchingRecordStatus status,
            Pageable pageable) {

        if (status == null) return new ResponseEntity("status shouldn't be null", HttpStatus.BAD_REQUEST);

        Page records = this.getRecordsPageByStatus(id, status, pageable)
                .map(record -> projectionFactory.createProjection(PaymentMatchingRecordProjection.class, record));
        Double balanceSum = this.getPaymentsAmountByStatus(id, status);

        AccountingPage accountingPageResult =
                new AccountingPage(records.getContent(), pageable, records.getTotalElements(), balanceSum);

        return new ResponseEntity<>(accountingPageResult, HttpStatus.OK);
    }

    // Jirra ACC-1870
    @PreAuthorize("hasPermission('paymentsmatchingfiles','getrecords/by-status/csv')")
    @RequestMapping(value = "/getrecords/by-status/csv/{id}", method = RequestMethod.GET)
    public void exportRecordsByStatusAsCSV(@PathVariable(value = "id") Long id,
                                           @RequestParam(name = "status") PaymentMatchingRecordStatus status,
                                           HttpServletResponse response) throws Exception {

        if (status == null) throw new RuntimeException("status shouldn't be null");

        List<PaymentMatchingRecord> records = this.getRecordsByStatusForCSV(id, status);

        File csvFile = CsvHelper.generateCsv(records, PaymentMatchingRecordProjectionCSV.class, csvColumnTitles, csvColumn, "Payments Details");

        createDownloadResponse(response,
                "Payments Details.csv",
                new FileInputStream(csvFile));
    }

    private List<PaymentMatchingRecord> getRecordsListByStatus(Long fileId, PaymentMatchingRecordStatus status) {
        if (!Arrays.asList(PaymentMatchingRecordStatus.FAILED, PaymentMatchingRecordStatus.IN_PROGRESS, PaymentMatchingRecordStatus.MATCHED).contains(status)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdAndRecordStatus(fileId, status);
        }
        Date dateOfPayment = new DateTime().withYear(2019).withMonthOfYear(5).withDayOfMonth(19).toDate();
        if (status.equals(PaymentMatchingRecordStatus.MATCHED)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdAAndRecordStatusWithoutBackgroundTask(fileId, PaymentMatchingRecordStatus.MATCHED, dateOfPayment);
        }

        if (status.equals(PaymentMatchingRecordStatus.IN_PROGRESS)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdWithBackgroundTaskStatusNotIn(fileId, Arrays.asList(BackgroundTaskStatus.Failed, BackgroundTaskStatus.Finished), dateOfPayment);
        }

        if (status.equals(PaymentMatchingRecordStatus.FAILED)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdAndRecordStatusOrBackgroundTaskStatusEquals(fileId, status, BackgroundTaskStatus.Failed, dateOfPayment);
        }

        return new ArrayList();
    }

    private Page<PaymentMatchingRecord> getRecordsPageByStatus(Long fileId, PaymentMatchingRecordStatus status, Pageable pageable) {
        if (!Arrays.asList(PaymentMatchingRecordStatus.FAILED, PaymentMatchingRecordStatus.IN_PROGRESS, PaymentMatchingRecordStatus.MATCHED).contains(status)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdAndRecordStatus(fileId, status, pageable);
        }
        Date dateOfPayment = new DateTime().withYear(2019).withMonthOfYear(5).withDayOfMonth(19).toDate();
        if (status.equals(PaymentMatchingRecordStatus.MATCHED)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdAAndRecordStatusWithoutBackgroundTask(fileId, PaymentMatchingRecordStatus.MATCHED, dateOfPayment, pageable);
        }

        if (status.equals(PaymentMatchingRecordStatus.IN_PROGRESS)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdWithBackgroundTaskStatusNotIn(fileId, Arrays.asList(BackgroundTaskStatus.Failed, BackgroundTaskStatus.Finished), dateOfPayment, pageable);
        }

        if (status.equals(PaymentMatchingRecordStatus.FAILED)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdAndRecordStatusOrBackgroundTaskStatusEquals(fileId, status, BackgroundTaskStatus.Failed, dateOfPayment, pageable);
        }

        return new PageImpl(new ArrayList());
    }

    private Double getPaymentsAmountByStatus(Long fileId, PaymentMatchingRecordStatus status) {
        Double sum = null;
        if (!Arrays.asList(PaymentMatchingRecordStatus.FAILED, PaymentMatchingRecordStatus.IN_PROGRESS, PaymentMatchingRecordStatus.MATCHED).contains(status)) {
            sum = paymentMatchingRecordRepository.sumPaymentsAmountByPaymentsMatchingFileIdAndRecordStatus(fileId, status);
        }
        Date dateOfPayment = new DateTime().withYear(2019).withMonthOfYear(5).withDayOfMonth(19).toDate();
        if (status.equals(PaymentMatchingRecordStatus.MATCHED)) {
            sum = paymentMatchingRecordRepository.sumPaymentsAmountByPaymentsMatchingFileIdAAndRecordStatusWithoutBackgroundTask(fileId, PaymentMatchingRecordStatus.MATCHED, dateOfPayment);
        }

        if (status.equals(PaymentMatchingRecordStatus.IN_PROGRESS)) {
            sum = paymentMatchingRecordRepository.sumPaymentsAmountByPaymentsMatchingFileIdWithBackgroundTaskStatusNotIn(fileId, Arrays.asList(BackgroundTaskStatus.Failed, BackgroundTaskStatus.Finished), dateOfPayment);
        }

        if (status.equals(PaymentMatchingRecordStatus.FAILED)) {
            sum = paymentMatchingRecordRepository.sumPaymentsAmountByPaymentsMatchingFileIdAndRecordStatusOrBackgroundTaskStatusEquals(fileId, status, BackgroundTaskStatus.Failed, dateOfPayment);
        }

        return sum != null ? sum : 0.0;
    }

    private List<PaymentMatchingRecord> getRecordsByStatusForCSV(Long fileId, PaymentMatchingRecordStatus status) {
        if (!Arrays.asList(PaymentMatchingRecordStatus.FAILED, PaymentMatchingRecordStatus.IN_PROGRESS, PaymentMatchingRecordStatus.MATCHED).contains(status)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdAndRecordStatusForCSV(fileId, status);
        }
        Date dateOfPayment = new DateTime().withYear(2019).withMonthOfYear(5).withDayOfMonth(19).toDate();
        if (status.equals(PaymentMatchingRecordStatus.MATCHED)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdAAndRecordStatusWithoutBackgroundTaskForCSV(fileId, PaymentMatchingRecordStatus.MATCHED, dateOfPayment);
        }

        if (status.equals(PaymentMatchingRecordStatus.IN_PROGRESS)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdWithBackgroundTaskStatusNotInForCSV(fileId, Arrays.asList(BackgroundTaskStatus.Failed, BackgroundTaskStatus.Finished), dateOfPayment);
        }

        if (status.equals(PaymentMatchingRecordStatus.FAILED)) {
            return paymentMatchingRecordRepository.findByPaymentsMatchingFileIdAndRecordStatusOrBackgroundTaskStatusEquals(fileId, status, BackgroundTaskStatus.Failed, dateOfPayment);
        }

        return new ArrayList();
    }

    // Jirra ACC-599
    @PreAuthorize("hasPermission('paymentsmatchingfiles','confirmpayments')")
    @RequestMapping(value = "/confirmpayments/{id}", method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> confirmpayments(@RequestBody List<Long> ids) throws Exception {

        SelectQuery<BackgroundTask> query = new SelectQuery(BackgroundTask.class);
        query.filterBy("relatedEntityType", "=", "PaymentMatchingRecord");
        query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        List<BackgroundTask> result = query.execute();
        if (result == null || result.isEmpty()) {
            selfController.processRecords2(null, ids, null, null);

            Thread.sleep(5000);
            return new ResponseEntity("processing", HttpStatus.OK);
        } else
            throw new RuntimeException("This feature is requested before and still processing.");
    }

    //Jirra ACC-1466
    @PreAuthorize("hasPermission('paymentsmatchingfiles','confirmpayments')")
    @RequestMapping(value = "/confirmallpayments/{id}", method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> confirmallpayments(@PathVariable(value = "id") PaymentsMatchingFile file,
                                                @RequestParam("status") PaymentMatchingRecordStatus status) throws Exception {

        if (!Arrays.asList(PaymentMatchingRecordStatus.MATCHED, PaymentMatchingRecordStatus.FAILED, PaymentMatchingRecordStatus.IN_PROGRESS).contains(status))
            throw new RuntimeException("invalid status");

        SelectQuery<BackgroundTask> query = new SelectQuery(BackgroundTask.class);
        query.filterBy("relatedEntityType", "=", "PaymentMatchingRecord");
        query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        List<BackgroundTask> result = query.execute();
        if (result == null || result.isEmpty()) {

            List<PaymentMatchingRecord> records;

//            if (status.equals(PaymentMatchingRecordStatus.MATCHED))
//                records = file.getMatchedRecords()
//                        .stream().filter(pmr -> !pmr.getRecordStatus().equals(PaymentMatchingRecordStatus.CONFIRMED)).collect(Collectors.toList());
//            else
//                records = this.getRecordsListByStatus(file.getId(), status)
//                        .stream().filter(pmr -> !pmr.getRecordStatus().equals(PaymentMatchingRecordStatus.CONFIRMED)).collect(Collectors.toList());

            records = this.getRecordsListByStatus(file.getId(), status);

            User loggedUser = CurrentRequest.getUser();
            Set<Position> positions = loggedUser.getPositions();
            String emails = CurrentRequest.getUser().getEmail();

            selfController.processRecords2(file.getId(), records.stream().map(record -> record.getId()).collect(Collectors.toList()), positions, emails);
            Thread.sleep(5000);

            return new ResponseEntity("processing", HttpStatus.OK);
        } else
            throw new RuntimeException("This feature is requested before and still processing.");
    }

    //Jirra ACC-1870
    public void processRecords2(Long fileId, List<Long> ids, Set<Position> positions, String emails) {
        if (emails != null && !emails.isEmpty() && fileId == null)
            throw new RuntimeException("can't process and send mail");

        String email = emails;
        Set<Position> usedPositions = positions;
        List<Long> recordsIds = ids;
        logger.log(Level.SEVERE, prefix + "moved to in progress");

        for (Long paymentMatchingRecordId : recordsIds) {
            createBackgroundTask(paymentMatchingRecordId);
        }

    }

    public void createBackgroundTask(Long paymentMatchingRecordId) {
        taskExecutor.execute(new Runnable() {
            Long recordId = paymentMatchingRecordId;
            Long userId = CurrentRequest.getUser().getId();

            @Override
            public void run() {
                SecurityContext sc = SecurityContextHolder.getContext();
                try {
                    UsernamePasswordAuthenticationToken authReq = new UsernamePasswordAuthenticationToken(
                            Setup.getRepository(UserRepository.class).getOne(userId), null, null);
                    sc.setAuthentication(authReq);

                    PaymentMatchingRecord paymentMatchingRecord = paymentMatchingRecordRepository.findOne(recordId);
                    backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                            "PaymentsMatchingFile_" + paymentMatchingRecord.getPaymentsMatchingFile().getId(),
                            "paymentsMatchingFileController",
                            "accounting",
                            "processRecordRemotely",
                            "PaymentMatchingRecord",
                            recordId,
                            true,
                            false,
                            new Class<?>[]{Long.class},
                            new Object[]{recordId});
                } finally {
                    sc.setAuthentication(null);
                }
            }
        });

    }

    public void processRecordRemotely(Long paymentMatchingRecordId) throws Exception {
        PaymentMatchingRecord paymentMatchingRecord = paymentMatchingRecordRepository.findOne(paymentMatchingRecordId);

        try {
            if (!selfController.processRecord(paymentMatchingRecord)) {
                paymentMatchingRecord.setRecordStatus(PaymentMatchingRecordStatus.FAILED);
                paymentMatchingRecord.setTransferringFailureReason("payment is NULL or has an Invalid Status");
                paymentMatchingRecordRepository.save(paymentMatchingRecord);
            }
        } catch (Exception e) {
            paymentMatchingRecord.setRecordStatus(PaymentMatchingRecordStatus.FAILED);
            paymentMatchingRecord.setTransferringFailureReason(e.getMessage());
            paymentMatchingRecordRepository.save(paymentMatchingRecord);
            throw e;
        }
    }

    //Jirra ACC-1870
    @Transactional
    public boolean processRecord(PaymentMatchingRecord paymentMatchingRecord) throws Exception {

        logger.log(Level.SEVERE, prefix + "start processing for record: " + paymentMatchingRecord.getId());
        PaymentMatchingRecordRepository paymentMatchingRecordRepo = Setup.getRepository(PaymentMatchingRecordRepository.class);
        List<String> statuses =
                Arrays.asList(PaymentStatus.values()).stream()
                        .map(x -> x.name()).collect(Collectors.toList());

        if (!paymentMatchingRecord.getConfirmed()
                && paymentMatchingRecord.getPayment() != null
                && !PaymentMatchingRecord.notMatchingStatuses.contains(paymentMatchingRecord.getPayment().getStatus())) {
            logger.log(Level.SEVERE, prefix + "accepted");
            Payment p = paymentMatchingRecord.getPayment();
            String formattedStatus = paymentMatchingRecord.getStatus().trim();
            formattedStatus = formattedStatus.replace(" ", "_");
            formattedStatus = formattedStatus.toUpperCase();

            if (!statuses.contains(formattedStatus)) return false;

            PaymentStatus ps = PaymentStatus.valueOf(formattedStatus);
            paymentMatchingRecord.setPrevStatus(p.getStatus());
            // ACC-943
            if (ps.equals(PaymentStatus.BOUNCED)) {
                logger.log(Level.SEVERE, prefix + "bounced");
                if (paymentMatchingRecord.getReasonOfBouncedCheque() != null && !paymentMatchingRecord.getReasonOfBouncedCheque().isEmpty()) {
                    PicklistItem reasonOfBouncingCheque = getItem("ReasonOfBouncedCheck", paymentMatchingRecord.getReasonOfBouncedCheque());
                    if (reasonOfBouncingCheque != null) {
                        p.setReasonOfBouncingCheque(reasonOfBouncingCheque);
                    }
                }
                p.setDateOfBouncing(new java.sql.Date(new Date().getTime()));
            }

            p.setStatus(ps);
            Setup.getApplicationContext().getBean(PaymentService.class)
                    .forceUpdatePayment(p);

            paymentMatchingRecord.setConfirmed(true);
            paymentMatchingRecord.setRecordStatus(PaymentMatchingRecordStatus.CONFIRMED);

            logger.log(Level.SEVERE, prefix + "CONFIRMED");
            paymentMatchingRecordRepo.save(paymentMatchingRecord);
            logger.log(Level.SEVERE, prefix + "SAVED");

            return true;
        } else
            return false;
    }

    //Jirra ACC-1870
    @PreAuthorize("hasPermission('paymentsmatchingfiles','check-thread-status')")
    @RequestMapping(value = "/check-thread-status", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity checkThreadStatus() {
        SelectQuery<BackgroundTask> query = new SelectQuery(BackgroundTask.class);
        query.filterBy("relatedEntityType", "=", "PaymentMatchingRecord");
        query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        List<BackgroundTask> result = query.execute();
        if (result == null || result.isEmpty()) {
            return new ResponseEntity("Finished Successfully", HttpStatus.OK);
        } else
            return new ResponseEntity("still processing", HttpStatus.OK);
    }
}
