package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.entity.AmwalStatementRecord;
import com.magnamedia.entity.NoqodiStatementRecord;
import com.magnamedia.entity.VisaStatement;
import com.magnamedia.entity.VisaStatementTransaction;
import com.magnamedia.extra.*;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.repository.AmwalStatementRecordRepository;
import com.magnamedia.repository.NoqodiStatementRecordRepository;
import com.magnamedia.repository.VisaStatementRepository;
import com.magnamedia.repository.VisaStatementTransactionRepository;
import com.magnamedia.service.VisaStatementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping("/visaStatement")
@RestController
public class VisaStatementController extends BaseRepositoryController<VisaStatement> {

    @Autowired
    private VisaStatementRepository visaStatementRepository;

    @Autowired
    private VisaStatementTransactionRepository statementTransactionRepository;

    @Autowired
    private VisaStatementService service;

    @Override
    public BaseRepositoryParent<VisaStatement> getRepository() {
        return visaStatementRepository;
    }


    @PreAuthorize("hasPermission('visaStatement','allStatements')")
    @RequestMapping(value = "/allStatements", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> allStatements(
            @RequestParam(required = false) VisaStatementType type, Pageable page) {

        SelectQuery query = new SelectQuery(VisaStatement.class);
        if (type != null) {
            query.filterBy("type", "=", type);
        }
        query.sortBy("creationDate",false);
        return new ResponseEntity<>(query.execute(page), HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('visaStatement','confirm')")
    @RequestMapping(value = "/confirm/{id}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> confirmVisaStatement(
            @PathVariable("id") VisaStatement statement) {

        VisaStatementTransaction transaction = statementTransactionRepository.findTopByStatementAndFinished(statement,false);
        if (transaction != null){
            throw new RuntimeException("there are some transactions without processing");
        }
        statement.setStatus(VisaStatementStatus.CONFIRMED);
        visaStatementRepository.save(statement);
        return new ResponseEntity<>("Done.", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatement','canBeConfirm')")
    @RequestMapping(value = "/canBeConfirm/{id}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> canBeConfirmVisaStatement(
            @PathVariable("id") VisaStatement statement) {

        VisaStatementTransaction transaction = statementTransactionRepository
                .findTopByStatementAndTypeNotAndFinishedFalse(statement,
                        VisaStatementTransactionType.MissingFromStatement);

        if (transaction == null) return new ResponseEntity<>(true, HttpStatus.OK);
        return new ResponseEntity<>(false, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('visaStatement','startParsing')")
    @RequestMapping(value = "/startParsing", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> startParsing(@RequestBody VisaStatement statement){

        statement.setStatus(VisaStatementStatus.INITIAL);
        statement = visaStatementRepository.save(statement);

        Map<String, Object> payload = new HashMap<>();
        payload.put("entityId", statement.getId());
        BackgroundTaskHelper.createBGTParsingStatementUploaded(UploadStatementEntityType.VisaStatement,
                        "create_all_visa_transactions_from_file_" + statement.getId(),
                          payload);

        return new ResponseEntity<>(statement, HttpStatus.OK);
    }

    @Override
    protected ResponseEntity<?> deleteEntity(VisaStatement entity) {
        if (entity.getCanBeDeleted() == null || !entity.getCanBeDeleted()){
            throw new RuntimeException("Can't be deleted! some actions were already taken");
        }

        if (VisaStatementType.Amwal.equals(entity.getType())){
            List<AmwalStatementRecord> amwalRecords = Setup.getRepository(AmwalStatementRecordRepository.class)
                    .findByStatement(entity);
            Setup.getRepository(AmwalStatementRecordRepository.class).deleteAll(amwalRecords);

        } else if (VisaStatementType.Noqodi.equals(entity.getType())) {
            List<NoqodiStatementRecord> noqodiRecords = Setup.getRepository(NoqodiStatementRecordRepository.class)
                    .findByStatement(entity);
            Setup.getRepository(NoqodiStatementRecordRepository.class).deleteAll(noqodiRecords);
        }

        List<VisaStatementTransaction> transactionRecords = statementTransactionRepository
                .findByStatement(entity);
        statementTransactionRepository.deleteAll(transactionRecords);

        return super.deleteEntity(entity);
    }

}
