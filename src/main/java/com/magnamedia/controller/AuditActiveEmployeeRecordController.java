package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.entity.AuditActiveEmployeeRecord;
import com.magnamedia.entity.AuditActiveEmployeesFile;
import com.magnamedia.extra.AuditActiveEmployeeRecordType;
import com.magnamedia.repository.AuditActiveEmployeeRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("auditActiveEmployeeRecord")
public class AuditActiveEmployeeRecordController extends BaseRepositoryController<AuditActiveEmployeeRecord> {

    @Autowired
    private AuditActiveEmployeeRecordRepository auditActiveEmployeeRecordRepository;

    @Override
    public BaseRepositoryParent<AuditActiveEmployeeRecord> getRepository() {
        return auditActiveEmployeeRecordRepository;
    }

    @PreAuthorize("hasPermission('auditActiveEmployeeRecord','getActiveOnERPButNotOnFile')")
    @RequestMapping(value = "/getActiveOnERPButNotOnFile/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getActiveOnERPButNotOnFile(@PathVariable("id") AuditActiveEmployeesFile entity, Pageable page) {
        SelectQuery query = new SelectQuery(AuditActiveEmployeeRecord.class);
        query.filterBy("file", "=", entity);
        query.filterBy("type", "=", AuditActiveEmployeeRecordType.ACTIVE_ON_ERP_BUT_TERMINATED_ON_FILE);

        return new ResponseEntity<>(query.execute(page), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('auditActiveEmployeeRecord','getActiveOnFileButNotOnERP')")
    @RequestMapping(value = "/getActiveOnFileButNotOnERP/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getActiveOnFileButNotOnERP(@PathVariable("id") AuditActiveEmployeesFile entity, Pageable page) {
        SelectQuery query = new SelectQuery(AuditActiveEmployeeRecord.class);
        query.filterBy("file", "=", entity);
        query.filterBy("type", "=", AuditActiveEmployeeRecordType.TERMINATED_ON_ERP_BUT_ACTIVE_ON_FILE);

        return new ResponseEntity<>(query.execute(page), HttpStatus.OK);
    }
}