package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.CooExpenseNotification;
import com.magnamedia.repository.CooExpenseNotificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cooexpensenotification")
public class CooExpenseNotificationController extends BaseRepositoryController<CooExpenseNotification> {
    @Autowired
    private CooExpenseNotificationRepository cooExpenseNotificationRepository;

    @Override
    public BaseRepository<CooExpenseNotification> getRepository() {
        return cooExpenseNotificationRepository;
    }

    @PreAuthorize("hasPermission('cooexpensenotification','dismiss')")
    @RequestMapping(value = "/dismiss", method = RequestMethod.GET)
    public ResponseEntity<?> dismiss(@RequestParam(name = "id", required = false) Long id ) {
        CooExpenseNotification cooExpenseNotification = getRepository().getOne(id);
        if(cooExpenseNotification.getHidden())
            return new ResponseEntity("Sorry, Notification is already dismissed!", HttpStatus.BAD_REQUEST);

        cooExpenseNotification.setHidden(true);
        getRepository().save(cooExpenseNotification);
        return new ResponseEntity("Done!", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('cooexpensenotification','index')")
    @RequestMapping(value = "/allnotdismissed", method = RequestMethod.GET)
    public ResponseEntity<?> allnotdismissed(Pageable pageable) {
        SelectQuery<CooExpenseNotification> query = new SelectQuery<>(CooExpenseNotification.class);
        query.filterBy(new SelectFilter("hidden" , "=" , false)
                .or("hidden", "IS NULL", null));
        return new ResponseEntity(query.execute(pageable), HttpStatus.OK);

    }
}
