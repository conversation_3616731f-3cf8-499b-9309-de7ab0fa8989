package com.magnamedia.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Iterables;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.ContractPaymentSearchProjection;
import com.magnamedia.entity.projection.ContractPaymentSearchProjectionCsv;
import com.magnamedia.entity.projection.DDContractPaymentSearchProjectionCsv;
import com.magnamedia.extra.DirectDebitContratPayment;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.repository.ContractPaymentRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.MessagingService;
import com.magnamedia.service.ContractPaymentConfirmationToDoService;
import com.magnamedia.service.ContractPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.logging.Level;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Dec 30, 2018
 */
@RestController
@RequestMapping("/contractpayments")
public class ContractPaymentController extends BaseRepositoryController<ContractPayment> {

    @Autowired
    private ContractPaymentRepository contractPaymentRepository;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Autowired
    private ContractPaymentRepository contractPaymentRep;

    @Autowired
    private DirectDebitRepository directDebitRep;

    @Autowired
    private Utils utils;

    @Autowired
    private ContractPaymentTermController contractPaymentTermCntlr;

    @Autowired
    private ContractPaymentConfirmationToDoService paymentConfirmationService;
    @Autowired
    private ContractPaymentService contractPaymentService;

    @Override
    public BaseRepository<ContractPayment> getRepository() {
        return contractPaymentRepository;
    }

    //Jirra ACC-331
    @PreAuthorize("hasPermission('contractpayments','isconfirmed')")
    @RequestMapping(value = "/isconfirmed",
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> isConfirmed(
            @RequestBody List<FilterItem> filters,
            Pageable pageable) {
        SelectQuery<ContractPayment> query =
                new SelectQuery<>(ContractPayment.class);
        //Process Filters
        //Jirra ACC-390
        SelectFilter selectFilter = new SelectFilter("paymentMethod", "<>", PaymentMethod.DIRECT_DEBIT);

        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(ContractPayment.class));
        }
        query.filterBy(selectFilter);
        //Sorting
        if (pageable.getSort() == null || Iterables.isEmpty(pageable.getSort())) {
            query.sortBy("creationDate", true, false);
        }

        return new ResponseEntity<>(
                query.execute(pageable).map(
                        c -> projectionFactory.createProjection(
                                ContractPaymentSearchProjection.class, c)),
                HttpStatus.OK);
    }

    //Jirra ACC-494
    @PreAuthorize("hasPermission('contractpayments','isconfirmed')")
    @RequestMapping(value = "/isconfirmed/csv",
            method = RequestMethod.POST)
    @ResponseBody
    public void isConfirmedCsv(
            @RequestBody List<FilterItem> filters,
            Sort sort,
            @RequestParam(name = "limit", required = false) Integer limit,
            HttpServletResponse response) {
        if (limit == null) {
            limit = 1000;
        }
        SelectQuery<ContractPayment> query =
                new SelectQuery<>(ContractPayment.class);
        SelectFilter selectFilter = new SelectFilter("paymentMethod", "<>", PaymentMethod.DIRECT_DEBIT);

        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(ContractPayment.class));
        }
        query.filterBy(selectFilter);

        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("creationDate", true, false);
        }

        InputStream is = null;
        
        try {
            String[] namesOrdared =
                    {"client", "housemaid", "contractId", "contractType", "nationality", "paymentDate",
                            "amount", "paymentType", "paymentMethod", "additionalDiscountAmount", "additionalDiscountNotes", "isActive"};
            is = generateCsv(query, ContractPaymentSearchProjectionCsv.class, namesOrdared, limit);
            createDownloadResponse(response,
                    "ContractPayments.csv",
                    is);
        } catch (IOException ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    //Jirra ACC-391 ACC-457
    @PreAuthorize("hasPermission('contractpayments','ddpayments')")
    @RequestMapping(value = "/ddpayments",
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> ddPayments(
            @RequestBody List<FilterItem> filters,
            Pageable pageable) {

        EntityManager em = Setup.getEntityManagerFactory().createEntityManager();

        try {
            Map<Integer, Object> parametrs = new HashMap<>();
            String queryStr = buildDDPaymentsQuery(filters, parametrs, pageable);
            TypedQuery<DirectDebitContratPayment> query =
                    em.createQuery(queryStr, DirectDebitContratPayment.class);
            for (Integer index : parametrs.keySet())
                query.setParameter(index, parametrs.get(index));
            List<DirectDebitContratPayment> result = query.getResultList();

            //ListToPage
            int start = (int) pageable.getOffset();
            int end = ((start + pageable.getPageSize()) > result.size()) ?
                    result.size() : (start + pageable.getPageSize());

            PageImpl<DirectDebitContratPayment> page = new PageImpl<>(
                    start > end ? new ArrayList<>()
                            : result.subList(start, end),
                    pageable, result.size());

            return new ResponseEntity<>(page, HttpStatus.OK);
        } finally {
            em.close();
        }
    }

//    public void sendMail(DirectDebitFile file) {
//        Client client =
//                file.getDirectDebit().getContractPaymentTerm().getContract().getClient();
//        EmailRecipient recipient =
//                new Recipient(client.getEmail(), client.getName());
//        List<EmailRecipient> recipients = new ArrayList();
//        recipients.add(recipient);
//        //distingush email greetings
//        String greetings;
//        if (file.getDirectDebit().getContractPaymentTerm()
//                .getContract().getContractProspectType().getCode()
//                .equals(PicklistItem.getCode(
//                        AccountingModule.MAID_VISA_PEOSPECT_TYPE)))
//            greetings =
//                    Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA);
//        else
//            greetings =
//                    Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC);
//
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("greetings", greetings);
//        parameters.put("client_name", client.getName() != null ? client.getName() : "");
//        parameters.put("PARAMETER_RENEWAL_PAGE_WHATSAPP_LINK", Setup.getParameter(Setup.getCurrentModule(),
//                AccountingModule.PARAMETER_RENEWAL_PAGE_WHATSAPP_LINK));
//
//        TemplateEmail templateEmail = new TemplateEmail("Direct Debit File", "ddf_email", parameters);
//
//        List<Attachment> ddfAttachment = file.getAttachments().stream()
//                .filter(attach -> attach.getTag().startsWith(DirectDebitFile.FILE_TAG_DD_ACTIVATION))
//                .collect(Collectors.toList());
//        for (Attachment attach : ddfAttachment) {
//            templateEmail.addAttachement(attach);
//        }
//        Properties emailProperties =
//                Setup.getApplicationContext().getBean(Utils.class)
//                        .getClientEmailSenderProperties(
//                                file.getDirectDebit().getContractPaymentTerm()
//                                        .getContract().getContractProspectType()
//                                        .getCode().equals(
//                                        PicklistItem.getCode(
//                                                AccountingModule.MAID_VISA_PEOSPECT_TYPE)));
//
//        Setup.getMailService().sendmail(recipients, templateEmail, emailProperties, null);
//    }

    //Jirra ACC-494
    @PreAuthorize("hasPermission('contractpayments','ddpayments')")
    @RequestMapping(value = "/ddpayments/csv",
            method = RequestMethod.POST)
    @ResponseBody
    public void ddPaymentsCsv(
            @RequestBody List<FilterItem> filters,
            Pageable pageable,
            @RequestParam(name = "limit", required = false) Integer limit,
            HttpServletResponse response) {

        EntityManager em = Setup.getEntityManagerFactory().createEntityManager();

        InputStream is = null;
        try {
            Map<Integer, Object> parametrs = new HashMap<>();
            String queryStr = buildDDPaymentsQuery(filters, parametrs, pageable);
            TypedQuery<DirectDebitContratPayment> query =
                    em.createQuery(queryStr, DirectDebitContratPayment.class);
            for (Integer index : parametrs.keySet())
                query.setParameter(index, parametrs.get(index));
            List<DirectDebitContratPayment> result = query.getResultList();

            String[] namesOrdared = {"client", "housemaid", "contractID", "contractType",
                    "nationality", "paymentDate", "paymentType", "paymentMethod",
                    "creationDate", "amount", "numberOfPayments"};
            is = generateCsv(result, DDContractPaymentSearchProjectionCsv.class, namesOrdared);
            createDownloadResponse(response,"ContractPayments.csv", is);

        } catch (IOException ex) {
            logger.log(Level.SEVERE, ex.getMessage(), ex);
        } finally {
            StreamsUtil.closeStream(is);
            em.close();
        }
    }

    private String buildDDPaymentsQuery(
            List<FilterItem> filters, Map<Integer, Object> parametrs, Pageable pageable) {

        String queryStr = "";

        queryStr += "SELECT new com.magnamedia.extra.DirectDebitContratPayment("
                + "cl, h, c, n, pt, dd, COUNT(distinct cp)) FROM ";

        queryStr += "ContractPayment cp "
                + "JOIN cp.directDebit dd "
                + "LEFT JOIN cp.directDebit.directDebitFiles df "
                + "LEFT JOIN cp.contractPaymentTerm cpt "
                + "LEFT JOIN cp.contractPaymentTerm.contract c "
                + "LEFT JOIN cp.contractPaymentTerm.contract.client cl "
                + "LEFT JOIN cp.contractPaymentTerm.contract.housemaid h "
                + "LEFT JOIN cp.contractPaymentTerm.contract.housemaid.nationality n "
                + "LEFT JOIN cp.paymentType pt "
                + "WHERE cp.paymentMethod = com.magnamedia.module.type.PaymentMethod.DIRECT_DEBIT "
                + "AND dd.status = com.magnamedia.extra.DirectDebitStatus.PENDING ";
        //+ "AND dd.attachmentsCount > 1 ";

        int i = 0;
        for (FilterItem filter : filters) {
            i++;
            SelectFilter selectFilter = filter.getSelectFilter(ContractPayment.class);
            parametrs.put(i, selectFilter.getValue());
            queryStr += "AND " + setAliases(selectFilter.getField()) + " " + selectFilter.getOperation() + " ?" + i + " ";
        }
        queryStr += "GROUP BY cl, h, c, n, pt, dd ";
        queryStr += "HAVING count(DISTINCT df) > 1 ";
        queryStr += "ORDER BY ";
        if (pageable.getSort() != null) {
            for (Sort.Order order : pageable.getSort()) {
                queryStr +=
                        "cp." + order.getProperty() +
                                (order.isAscending() ? " ASC " : " DESC ") +
                                ", ";
            }
        }
        queryStr += "cp.creationDate ASC ";
        return queryStr;

    }

    private String setAliases(String s) {
        s = "cp." + s;
        s = s.replace("cp.contractPaymentTerm.contract.housemaid.nationality", "n");
        s = s.replace("cp.contractPaymentTerm.contract.housemaid", "h");
        s = s.replace("cp.contractPaymentTerm.contract.client", "cl");
        s = s.replace("cp.contractPaymentTerm.contract", "c");
        s = s.replace("cp.contractPaymentTerm", "cpt");
        s = s.replace("ContractPayment", "cp");
        s = s.replace("cp.directDebit", "dd");
        s = s.replace("cp.paymentType", "pt");
        return s;
    }

    //Jirra ACC-1435 from here
    @Transactional
    @PreAuthorize("hasPermission('contractpayments','SavePayments')")
    @RequestMapping(value = "/savepayments/{CONTARCT_PAYMENT_TERM_ID}", method = RequestMethod.POST)
    public ResponseEntity savePaymentsAPI(@PathVariable("CONTARCT_PAYMENT_TERM_ID") ContractPaymentTerm contractPaymentTerm,
                                          @RequestBody List<JsonNode> paymentsJson) {
        List<ContractPayment> payments = utils.readObjectsFromJson(paymentsJson, contractPaymentRep,
                ContractPayment.class);
        contractPaymentService.savePayments(contractPaymentTerm, payments, null);
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @Transactional
    @PreAuthorize("hasPermission('contractpayments','deleteDirectDebitPayments')")
    @RequestMapping(value = "/deletedirectdebitpayments/{id}")
    public ResponseEntity<?> deleteDirectDebitPayments(@PathVariable("id") DirectDebit directDebit) {
        contractPaymentRep.findByDirectDebit(directDebit).forEach(payment -> {
            contractPaymentRep.delete(payment);
        });
        directDebitRep.delete(directDebit);
        return new ResponseEntity<>("Done", HttpStatus.OK);
    }


    @Transactional
    @PreAuthorize("hasPermission('contractpayments','addPaymentsByAccountant')")
    @PostMapping("/addpaymentsbyaccountant/{CONTARCT_ID}")
    public ResponseEntity<?> addPaymentsByAccountant(
            @PathVariable("CONTARCT_ID") Contract contract,
            @RequestBody List<JsonNode> paymentsJson) {

        ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();
        List<ContractPayment> payments = utils.readObjectsFromJson(
                paymentsJson, contractPaymentRep, ContractPayment.class);

        addPaymentsByAccountant(contractPaymentTerm, payments);

        return new ResponseEntity("Done", HttpStatus.OK);
    }
    
    @Transactional
    public List<ContractPayment> addPaymentsByAccountant(
            ContractPaymentTerm contractPaymentTerm,
            List<ContractPayment> payments) {
        
        for (ContractPayment payment : payments) {
            if (payment.getPaymentMethod().equals(PaymentMethod.DIRECT_DEBIT)) {
                throw new RuntimeException("cannot add direct debit payments");
            }
            payment.setAddedByAccountant(true);
        }
        contractPaymentService.savePayments(contractPaymentTerm, payments, null);
        return payments;
    }
    // ACC-1435 to here
}
