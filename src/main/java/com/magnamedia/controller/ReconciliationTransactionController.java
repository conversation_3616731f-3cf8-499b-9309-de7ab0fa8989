package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.ReconciliationTransaction;
import com.magnamedia.repository.ReconciliationTransactionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Mamon.Masod on 4/27/2021.
 */

@RestController
@RequestMapping("/reconciliation-transaction")
public class ReconciliationTransactionController extends BaseRepositoryController<ReconciliationTransaction> {

    @Autowired
    private ReconciliationTransactionRepository reconciliationTransactionRepository;

    @Override
    public BaseRepository<ReconciliationTransaction> getRepository() {
        return reconciliationTransactionRepository;
    }
}
