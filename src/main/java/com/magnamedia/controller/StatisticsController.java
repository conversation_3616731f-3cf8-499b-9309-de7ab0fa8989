package com.magnamedia.controller;


import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.report.OCR_Stats_Report;
import com.magnamedia.repository.DirectDebitRepository;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.*;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Sep 06, 2020
 *         Jirra ACC-2523
 */

@RestController
@RequestMapping("/stats")
public class StatisticsController {

    @Autowired
    private DirectDebitRepository directDebitRepository;

    @PreAuthorize("hasPermission('stats','ocr')")
    @RequestMapping("/ocr")
    public ResponseEntity getStats() throws Exception {
        List<Map> response = new ArrayList();

        List<String> tags = Arrays.asList(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME,
                ContractPaymentTermController.FILE_TAG_BANK_INFO_EID, ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN);

        OCR_Stats_Report report = new OCR_Stats_Report();
        DateTime now = new DateTime().withTimeAtStartOfDay();
        DateTime lastWeekDate = now.minusDays(7);
        DateTime lastMonthDate = now.minusMonths(1);

        for (String tag : tags) {
            Map<String, Object> newRow = new HashMap();
            List<Map<String, Object>> result = directDebitRepository.getOCRStatistics(now.toDate(), tag);
            Map<String, Object> todayStats = result != null && !result.isEmpty() ? result.get(0) : null;
            result = directDebitRepository.getOCRStatistics(lastWeekDate.toDate(), tag);
            Map<String, Object> lastWeekStats = result != null && !result.isEmpty() ? result.get(0) : null;
            result = directDebitRepository.getOCRStatistics(lastMonthDate.toDate(), tag);
            Map<String, Object> lastMonthStats = result != null && !result.isEmpty() ? result.get(0) : null;

            newRow.put("function", normalizeTag(tag));
            newRow.put("today", calculateRatio(todayStats));
            newRow.put("lastWeek", calculateRatio(lastWeekStats));
            newRow.put("lastMonth", calculateRatio(lastMonthStats));

//            report.addRow(normalizeTag(tag), newRow);
            response.add(newRow);
        }

        return new ResponseEntity(response, HttpStatus.OK);
    }

    private String calculateRatio(Map<String, Object> map) {
        if (map == null) return "0";

//        Long unknown = map.get("UN_KNOWN") != null ? ((BigInteger) map.get("UN_KNOWN")).longValue() : 0L;
        Long failed = map.get("FAILED") != null ? ((BigInteger) map.get("FAILED")).longValue() : 0L;
        Long success = map.get("SUCCESS") != null ? ((BigInteger) map.get("SUCCESS")).longValue() : 0L;

        if (failed == 0L && success == 0L) return "0";

        return "" + Math.round(success * 100 / (success + failed)) + " (" + success + "/" + (success + failed) + ")";
    }

    private String normalizeTag(String tag) {
        if (tag.contains("eid")) return "EID";

        if (tag.contains("iban")) return "IBAN";

        else return "Account Name";
    }
}
