package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.Aggregate;
import com.magnamedia.core.helper.AggregateQuery;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.CooQuestion;
import com.magnamedia.entity.NonClientPDC;
import com.magnamedia.entity.TenancyContract;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.report.TemplatedReport;
import com.magnamedia.repository.CooQuestionRepository;
import com.magnamedia.repository.NonClientPDCRepository;
import com.magnamedia.scheduledjobs.PdcDueDateEmailScheduledJob;

import java.text.NumberFormat;
import java.util.*;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 *
 * <AUTHOR> Kanaan <<EMAIL>>
 * Created on Dec 28, 2017
 */
@RequestMapping("/nonclientpdc")
@RestController
public class NonClientPDCController extends BaseRepositoryController<NonClientPDC> {

    private PdcDueDateEmailScheduledJob job;
    
    @Autowired
    private NonClientPDCRepository nonClientPDCRepositoryrepo;

    @Autowired
    ProjectionFactory projectionFactory;

    @Override
    public BaseRepository<NonClientPDC> getRepository() {
        return this.nonClientPDCRepositoryrepo;
    }

    @PreAuthorize("hasPermission('nonclientpdc','filter')")
    @RequestMapping(value = "/filter", method = RequestMethod.POST)
    @Transactional
    @ResponseBody
    public ResponseEntity<?> filter(@RequestBody List<FilterItem> filters,
            Pageable pageable, Sort sort) {

        SelectQuery<NonClientPDC> query = new SelectQuery<>(NonClientPDC.class);
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(NonClientPDC.class));
        }
        query.filterBy(selectFilter);

        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("chequeDueDate", false, true);
        }

        return new ResponseEntity<>(
                query.execute(pageable),
                 HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('nonclientpdc','filtersumpdcs')")
    @RequestMapping(value = "/filtersumpdcs", method = RequestMethod.POST)
    @Transactional
    @ResponseBody
    public ResponseEntity<?> filterSumPDCs(@RequestBody List<FilterItem> filters,
            Pageable pageable) {

        SelectQuery<NonClientPDC> query = new SelectQuery<>(NonClientPDC.class);
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(NonClientPDC.class));
        }
        query.filterBy(selectFilter);

        AggregateQuery aggQuery = new AggregateQuery(query, Aggregate.Sum, "amount");
        return new ResponseEntity<>(aggQuery.execute().doubleValue(), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('nonclientpdc','withdrawpdc')")
    @RequestMapping(value = "/withdrawpdc/{id}", method = RequestMethod.GET)
    @Transactional
    @ResponseBody
    public ResponseEntity<?> withdrawPDC(
            @PathVariable("id") NonClientPDC pdc) {
        pdc.setWithdrawn(Boolean.TRUE);
        super.updateEntity(pdc);
        return ResponseEntity.ok("The PDC has been withdrawn successfully.");
    }

    @PreAuthorize("hasPermission('nonclientpdc','bouncepdc')")
    @RequestMapping(value = "/bouncepdc/{id}", method = RequestMethod.GET)
    @Transactional
    @ResponseBody
    public ResponseEntity<?> bouncePDC(
            @PathVariable("id") NonClientPDC pdc) {
        pdc.setBounced(Boolean.TRUE);
        super.updateEntity(pdc);
        return ResponseEntity.ok("The PDC has been bounced successfully.");
    }

    @PreAuthorize("hasPermission('nonclientpdc','byTenancyContract')")
    @RequestMapping(value = "/byTenancyContract/{id}", method = RequestMethod.GET)

    public ResponseEntity<?> byTenancyContract(@PathVariable("id") TenancyContract contract,
            Pageable pageable) {

        SelectQuery<NonClientPDC> query = new SelectQuery<>(NonClientPDC.class);

        query.filterBy("tenancyContract", "=", contract);

        return new ResponseEntity<>(query.execute(pageable), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('nonclientpdc','sendMail')")
    @RequestMapping(value = "/sendMail", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> sendMail() {
        try {
            job = new PdcDueDateEmailScheduledJob();
            job.SendEmail();
            return okResponse();
        } catch (Exception e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.OK);
        }

    }

    @PreAuthorize("hasPermission('nonclientpdc','getPDCsByDueDate')")
    @RequestMapping(value = "/getPDCsByDueDate", method = RequestMethod.GET)
    public ResponseEntity getPDCsByDueDate(@RequestParam(value = "startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                           @RequestParam(value = "archive") boolean archive,
                                           Pageable pageable) {
        int days = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PDCS_WITHIN_N_DAYS));
        if (!archive)
            return new ResponseEntity(nonClientPDCRepositoryrepo.findByDueDateBetweenAndDoneByCooFalse(
                        new DateTime(startDate).withTimeAtStartOfDay().toDate(),
                        new DateTime(startDate).plusDays(days).toDate(),
                        pageable).map(this::convertToNonClientPDCtDto),
                    HttpStatus.OK);

        return new ResponseEntity(nonClientPDCRepositoryrepo.findByDueDateBetween(
                new DateTime(startDate).withTimeAtStartOfDay().toDate(),
                new DateTime(startDate).plusDays(30).toDate(),
                pageable).map(this::convertToNonClientPDCtDto),
                HttpStatus.OK);
    }

    private Map<String, Object> convertToNonClientPDCtDto(Map<String, Object> nonClientPDC) {
        nonClientPDC.put("cooQuestionedPage", CooQuestion.QuestionedPage.NIGHT_REVIEW);
        nonClientPDC.put("amount", NumberFormat.getNumberInstance(Locale.US).format(nonClientPDC.get("amount")));
        nonClientPDC.put("chequeDueDate", TemplatedReport.format((Date) nonClientPDC.get("chequeDueDate")));
        nonClientPDC.put("contractEndDate", nonClientPDC.get("contractEndDate") == null
                ? "N\\A" : TemplatedReport.format((Date) nonClientPDC.get("chequeDueDate")));

        if (nonClientPDC.get("bankIssuerBucket") != null) {
            Bucket bucket = (Bucket) nonClientPDC.get("bankIssuerBucket");
            nonClientPDC.put("bankIssuerBucket", bucket == null ? "" : bucket.getName());
        }

        List<CooQuestion> cooQuestions = Setup.getRepository(CooQuestionRepository.class)
                .findByRelatedEntityIdAndRelatedEntityTypeAndQuestionedPage((Long) nonClientPDC.get("id"),
                        "NonClientPDC", CooQuestion.QuestionedPage.NIGHT_REVIEW);
        nonClientPDC.put("cooQuestions", cooQuestions);

        boolean allQuestionsAnswered = false, oneQuestionAnswered = false, noneQuestionAnswered = false;
        if (!cooQuestions.isEmpty()) {
            allQuestionsAnswered = cooQuestions.stream().allMatch(CooQuestion::isAnswered);
            oneQuestionAnswered = cooQuestions.stream().anyMatch(CooQuestion::isAnswered);
            noneQuestionAnswered = cooQuestions.stream().noneMatch(CooQuestion::isAnswered);
        }
        nonClientPDC.put("allQuestionsAnswered", allQuestionsAnswered);
        nonClientPDC.put("oneQuestionAnswered", oneQuestionAnswered);
        nonClientPDC.put("noneQuestionAnswered", noneQuestionAnswered);

        return nonClientPDC;
    }

    @PreAuthorize("hasPermission('nonclientpdc','mark-all-as-done-by-coo')")
    @RequestMapping(value = "/mark-all-as-done-by-coo", method = RequestMethod.GET)
    public ResponseEntity markAllAsDoneByCooAPI(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                                Pageable pageable) {
        int days = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PDCS_WITHIN_N_DAYS));
        nonClientPDCRepositoryrepo.findByDueDateBetweenAndDoneByCooFalse(
                new DateTime(startDate).withTimeAtStartOfDay().toDate(),
                new DateTime(startDate).plusDays(days).toDate(),
                pageable).getContent().forEach(o -> markAsDoneByCoo((Long) o.get("id")));
        return okResponse();
    }

    @PreAuthorize("hasPermission('nonclientpdc','mark-list-as-done-by-coo')")
    @RequestMapping(value = "/mark-list-as-done-by-coo", method = RequestMethod.POST)
    public ResponseEntity markListAsDoneByCooAPI(@RequestBody List<Long> ids) {
        ids.forEach(this::markAsDoneByCoo);
        return okResponse();
    }

    public void markAsDoneByCoo(Long id) {
        NonClientPDC nonClientPDC = nonClientPDCRepositoryrepo.findOne(id);
        nonClientPDC.setDoneByCoo(true);
        nonClientPDCRepositoryrepo.save(nonClientPDC);
    }
}
