package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.AuditActiveEmployeeRecord;
import com.magnamedia.entity.AuditActiveEmployeesFile;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.extra.AuditActiveEmployeeRecordCSVProjection;
import com.magnamedia.extra.AuditActiveEmployeeRecordType;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.repository.AuditActiveEmployeeRecordRepository;
import com.magnamedia.repository.AuditActiveEmployeesFileRepository;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@RestController
@RequestMapping("auditActiveEmployeesFile")
public class AuditActiveEmployeesFileController extends BaseRepositoryController<AuditActiveEmployeesFile> {

    @Autowired
    private AuditActiveEmployeesFileRepository auditActiveEmployeesFileRepository;

    @Autowired
    private AuditActiveEmployeeRecordRepository auditActiveEmployeeRecordRepository;

    private static final Logger logger = Logger.getLogger(AuditActiveEmployeesFileController.class.getName());

    @Override
    public BaseRepositoryParent getRepository() {
        return auditActiveEmployeesFileRepository;
    }

    @PreAuthorize("hasPermission('auditActiveEmployeesFile','allAuditFiles')")
    @RequestMapping(value = "/allAuditFiles", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> allAuditFiles(Pageable page){
        SelectQuery query = new SelectQuery(AuditActiveEmployeesFile.class);
        query.sortBy("creationDate",false);
        return new ResponseEntity<>(query.execute(page), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('auditActiveEmployeesFile','startParsing')")
    @RequestMapping(value = "/startParsing", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> startParsing(@RequestBody AuditActiveEmployeesFile entity) {

        entity = auditActiveEmployeesFileRepository.save(entity);

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "Parse audit employees file #" + entity.getId(),
                        "accounting",
                        "auditActiveEmployeesFileController",
                        "parseFile")
                        .withRelatedEntity("AuditActiveEmployeesFile", entity.getId())
                        .withParameters(
                                new Class[]{Long.class},
                                new Object[]{entity.getId()})
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .build());

        return new ResponseEntity<>(entity, HttpStatus.OK);
    }

    @Override
    protected ResponseEntity<?> deleteEntity(AuditActiveEmployeesFile entity) {
        List<AuditActiveEmployeeRecord> records = auditActiveEmployeeRecordRepository.findByFile(entity);
        auditActiveEmployeeRecordRepository.deleteAll(records);
        auditActiveEmployeesFileRepository.delete(entity);
        return super.deleteEntity(entity);
    }

    @Transactional
    public boolean parseFile(Long employeesFileId) {
        AuditActiveEmployeesFile entityAuditActiveEmployeesFile = auditActiveEmployeesFileRepository.findOne(employeesFileId);
        if (entityAuditActiveEmployeesFile == null) {
            throw new RuntimeException("AuditActiveEmployeesFile with id " + employeesFileId + " is not found");
        }

        Attachment auditFileAttachment = entityAuditActiveEmployeesFile.getAttachment("AUDIT_ACTIVE_EMPLOYEES_FILE");
        if (auditFileAttachment == null)
            throw new RuntimeException("No attachment in AuditActiveEmployeesFile with id " + employeesFileId);

        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            try {
                workbook = new XSSFWorkbook(Storage.getStream(auditFileAttachment));
            } catch (Exception e) {
                try {
                    workbook = new HSSFWorkbook(Storage.getStream(auditFileAttachment));
                } catch (Exception e1) {
                    logger.info("Error while parsing file " + auditFileAttachment.getName() + " for audit #" + entityAuditActiveEmployeesFile.getId());
                    throw new RuntimeException("Error while parsing file " + auditFileAttachment.getName());
                }
            }

            if (workbook != null) {
                NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);
                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> rowIterator = sheet.iterator();

                //by default
                int passportNumberColumnIndex = 57;
                if (rowIterator.hasNext()) {
                    Row firstRow = rowIterator.next();
                    //search for Passport Number cell index
                    for (Cell cell : firstRow) {
                        String cellText = formatter.formatCellValue(cell).trim();
                        if ("passport number".equalsIgnoreCase(cellText))
                            passportNumberColumnIndex = cell.getColumnIndex();
                    }
                }

                List<String> passports = new ArrayList<>();
                while (rowIterator.hasNext()) {
                    Row row = rowIterator.next();
                    String passportNumber = formatter.formatCellValue(row.getCell(passportNumberColumnIndex)).trim();

                    if (passportNumber == null || passportNumber.isEmpty())
                        break;

                    passports.add(passportNumber);

                }

                if (passports.isEmpty()) {
                    logger.info("empty file " + auditFileAttachment.getName() + " for file #" + entityAuditActiveEmployeesFile.getId());
                    throw new RuntimeException("Error while parsing file (empty file)" + auditFileAttachment.getName());
                }
                List<Housemaid> terminationMaids = getActiveMaidsInFileButNotInERP(passports, entityAuditActiveEmployeesFile);
                List<Housemaid> activeMaids = getActiveMaidsInERPButNotInFile(passports, entityAuditActiveEmployeesFile);

                List<OfficeStaff> terminationStaff = getActiveStaffsInFileButNotInERP(passports, entityAuditActiveEmployeesFile);
                List<OfficeStaff> activeStaff = getActiveStaffsInERPButNotInFile(passports, entityAuditActiveEmployeesFile);

                List<AuditActiveEmployeeRecord> records;
                records = terminationMaids.stream().map(x -> new AuditActiveEmployeeRecord(entityAuditActiveEmployeesFile, AuditActiveEmployeeRecordType.TERMINATED_ON_ERP_BUT_ACTIVE_ON_FILE, x))
                        .collect(Collectors.toList());
                records.addAll(activeMaids.stream().map(x -> new AuditActiveEmployeeRecord(entityAuditActiveEmployeesFile, AuditActiveEmployeeRecordType.ACTIVE_ON_ERP_BUT_TERMINATED_ON_FILE, x))
                        .collect(Collectors.toList()));

                records.addAll(terminationStaff.stream().map(x -> new AuditActiveEmployeeRecord(entityAuditActiveEmployeesFile, AuditActiveEmployeeRecordType.TERMINATED_ON_ERP_BUT_ACTIVE_ON_FILE, x))
                        .collect(Collectors.toList()));
                records.addAll(activeStaff.stream().map(x -> new AuditActiveEmployeeRecord(entityAuditActiveEmployeesFile, AuditActiveEmployeeRecordType.ACTIVE_ON_ERP_BUT_TERMINATED_ON_FILE, x))
                        .collect(Collectors.toList()));

                auditActiveEmployeeRecordRepository.saveAll(records);
            }
        } catch (Exception e) {
            logger.info("Error while parsing file " + auditFileAttachment.getName() + " for audit active employee file #" + entityAuditActiveEmployeesFile.getId() + " "+ Arrays.toString(e.getStackTrace()));
            throw new RuntimeException("Error while parsing file " + auditFileAttachment.getName()+e+Arrays.toString(e.getStackTrace()));
        }

        //set as finished parsing and save
        entityAuditActiveEmployeesFile.setFinishParsing(true);
        auditActiveEmployeesFileRepository.save(entityAuditActiveEmployeesFile);

        return true;
    }

    private List<OfficeStaff> getActiveStaffsInERPButNotInFile(
            List<String> passports, AuditActiveEmployeesFile entity) {

        return Setup.getRepository(OfficeStaffRepository.class)
                .getActiveStaffsInERPButNotInFile(DateUtil.getEndDayValue(entity.getAsOfDate()), passports);
    }

    private List<OfficeStaff> getActiveStaffsInFileButNotInERP(List<String> passports, AuditActiveEmployeesFile entity) {

        return Setup.getRepository(OfficeStaffRepository.class)
                .getActiveStaffsInFileButNotInERP(passports, DateUtil.getEndDayValue(entity.getAsOfDate()));
    }

    private List<Housemaid> getActiveMaidsInERPButNotInFile(
            List<String> passports, AuditActiveEmployeesFile entity) {

        return Setup.getRepository(HousemaidRepository.class)
                .getActiveMaidsInERPButNotInFile(DateUtil.getEndDayValue(entity.getAsOfDate()), passports);
    }

    private List<Housemaid> getActiveMaidsInFileButNotInERP(List<String> passports, AuditActiveEmployeesFile entity) {

        return Setup.getRepository(HousemaidRepository.class)
                .getActiveMaidsInERPButNotInFile(passports, DateUtil.getEndDayValue(entity.getAsOfDate()));
    }

    @PreAuthorize("hasPermission('auditActiveEmployeesFile','exportToCsv')")
    @GetMapping(value = "/exportToCsv/{id}")
    public void exportToCsv(
            @PathVariable("id") AuditActiveEmployeesFile entity,
            @RequestParam(name = "type") AuditActiveEmployeeRecordType type,
            HttpServletResponse response) {

        List<AuditActiveEmployeeRecord> records = auditActiveEmployeeRecordRepository.findByFileAndType(entity, type);
        File excelFile;
        InputStream inputStream = null;
        String fileName = "AuditActiveEmployees.csv";
        try {
            String[] namesOrdered = {"fullName", "passportNumber", "insuranceStartDate", "dateOfBirth", "nationality" };

            String[] headers = {"Full Name", "Passport Number", "Insurance Start Date", "DOB", "Nationality"};

            excelFile = CsvHelper.generateCsv(records, AuditActiveEmployeeRecordCSVProjection.class,
                    headers, namesOrdered, fileName);

            inputStream = new FileInputStream(excelFile);
            if (inputStream != null) {
                createDownloadResponse(response, fileName, inputStream);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            StreamsUtil.closeStream(inputStream);
        }
    }
}