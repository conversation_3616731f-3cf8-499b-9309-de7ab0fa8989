package com.magnamedia.controller;


import com.magnamedia.entity.AdhocVariableBucket;
import com.magnamedia.repository.AdhocVariableBucketRepository;
import com.magnamedia.repository.AdhocVariableNodeRepository;
import com.magnamedia.repository.BasePLVariableBucketRepository;
import com.magnamedia.repository.BasePLVariableRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@RequestMapping("/adhocvariablebuckets")
@RestController
public class AdhocVariableBucketController extends BasePLVariableBucketController<AdhocVariableBucket> {
    @Autowired
    private AdhocVariableBucketRepository adhocVariableBucketRepository;

    @Autowired
    private AdhocVariableNodeRepository adhocVariableNodeRepository;

    @Override
    public BasePLVariableRepository getPlVariableRepository() {
        return adhocVariableNodeRepository;
    }

    @Override
    public BasePLVariableBucketRepository<AdhocVariableBucket> getRepository() {
        return adhocVariableBucketRepository;
    }
}