package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Parameter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.ParameterRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.projection.PaymentRequestPurposeProjection;
import com.magnamedia.entity.projection.PaymentRequestPurposeProjectionCsv;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.repository.ClientRefundSetupRepository;
import com.magnamedia.repository.PaymentRequestPurposeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Aug 24, 2019
 *         ACC-837
 */
@RequestMapping("/paymentrequestpurposes")
@RestController
public class PaymentRequestPurposeContoller
        extends BaseRepositoryController<PaymentRequestPurpose> {

    private final static Logger logger = Logger.getLogger(PaymentRequestPurposeContoller.class.getName());

    @Autowired
    private PaymentRequestPurposeRepository paymentRequestPurposeRepository;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Autowired
    private PicklistItemRepository picklistItemRepository;

    @Override
    public BaseRepository<PaymentRequestPurpose> getRepository() {
        return paymentRequestPurposeRepository;
    }

    @PreAuthorize("hasPermission('paymentrequestpurposes','replacePurposeNameAndParameterContentClientWordAcc6602')")
    @GetMapping(value = "/replacePurposeNameAndParameterContentClientWordAcc6602")
    public ResponseEntity<?> replacePurposeNameAndParameterContentClientWordAcc6602() {

        logger.info("Change Parameter");

        ParameterRepository parameterRepository = Setup.getRepository(ParameterRepository.class);

        Parameter parameter = parameterRepository
                .findByModuleAndCode(Setup.getCurrentModule(), "payment_request_purpose_compensating_above_4000");
        if (parameter != null && !parameter.getValue().equals("Family’s compensation due to bad experience - according to number of days")) {
            parameter.setValue("Family’s compensation due to bad experience - according to number of days");
            parameterRepository.save(parameter);

        }

        parameter = parameterRepository
                .findByModuleAndCode(Setup.getCurrentModule(), "payment_request_purpose_compensating_below_4000");
        if (parameter != null && !parameter.getValue().equals("Compensating families for bad experience/days without service below AED 4000")) {
            parameter.setValue("Compensating families for bad experience/days without service below AED 4000");
            parameterRepository.save(parameter);

        }

        parameter = parameterRepository
                .findByModuleAndCode(Setup.getModule("clientmgmt"), "REFUSES_TO_JOIN_FLOW_REFUND_PURPOSE_NAME");
        if (parameter != null && !parameter.getValue().equals("Family’s compensation due to bad experience - not related to number of days")) {
            parameter.setValue("Family’s compensation due to bad experience - not related to number of days");
            parameterRepository.save(parameter);

        }
        logger.info("Done Change Parameter");

        SelectQuery<PaymentRequestPurpose> query = new SelectQuery<>(PaymentRequestPurpose.class);
        query.filterBy("name", "like", "%client%");
        List<PaymentRequestPurpose> result = query.execute();

        if (result.isEmpty()) return okResponse();
        logger.info("Change Purpose Name");

        for (PaymentRequestPurpose purpose : result) {
            purpose.setName(purpose.getName()
                    .replaceAll("Clients", "Families")
                    .replaceAll("clients", "families")
                    .replaceAll("Client", "Family")
                    .replaceAll("client", "family")

            );
        }

        paymentRequestPurposeRepository.save(result);
        logger.info("Done Change Purpose Name");

        return okResponse();
    }

    @PreAuthorize("hasPermission('paymentrequestpurposes','getallclientspurpose')")
    @RequestMapping(value = "/getallclientspurpose")
    public ResponseEntity<?> getAllClientsPurpose(
            @RequestParam(name = "category", required = false) Long categoryID,
            @RequestParam(name = "search", required = false) String search

    ) {
        SelectQuery<PaymentRequestPurpose> query = new SelectQuery<>(PaymentRequestPurpose.class);
        query.filterBy("forClient", "=", true);
        query.filterBy("active", "=", true);
        if (search != null && !search.isEmpty())
            query.filterBy("name", "like", "%"+search+"%");
        if (categoryID != null){
            query.filterBy("refundCategory", "like", picklistItemRepository.findOne(categoryID));
        }
        
        return new ResponseEntity<>(query.execute(), HttpStatus.OK);
//        if (search == null || search.isEmpty())
//            return new ResponseEntity<>(
//                    paymentRequestPurposeRepository.findByForClientAndActive(true, true),
//                    HttpStatus.OK);
//        else
//            return new ResponseEntity<>(
//                    paymentRequestPurposeRepository.findByForClientAndNameContainingAndActive(true, search, true),
//                    HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('paymentrequestpurposes','getallclientspurposewithsetup')")
    @RequestMapping(value = "/getallclientspurposewithsetup")
    public ResponseEntity<?> getAllClientsPurposeWithSetup(
            @RequestParam(name = "category", required = false) Long categoryID,
            @RequestParam(name = "search", required = false) String search) {

        List<Long> setupPurposeIds = Setup.getRepository(ClientRefundSetupRepository.class)
                .getClientPurposeIds();
        
        SelectQuery<PaymentRequestPurpose> query = new SelectQuery<>(PaymentRequestPurpose.class);
        query.filterBy("id", "in", setupPurposeIds);
        query.filterBy("forClient", "=", true);
        query.filterBy("active", "=", true);
        if (search != null && !search.isEmpty())
            query.filterBy("name", "like", "%"+search+"%");
        if (categoryID != null){
            query.filterBy("refundCategory", "like", picklistItemRepository.findOne(categoryID));
        }
        List<PaymentRequestPurpose> paymentRequestPurposes = query.execute();
//        if (search == null || search.isEmpty())
//            paymentRequestPurposes = paymentRequestPurposeRepository
//                    .findByForClientAndActiveAndIdIn(true, true, setupPurposeIds);
//        else
//            paymentRequestPurposes = paymentRequestPurposeRepository
//                    .findByForClientAndNameContainingAndActiveAndIdIn(true, search, true, setupPurposeIds);

        List<PaymentRequestPurposeProjection> result = paymentRequestPurposes.stream()
                .map(obj -> projectionFactory.createProjection(PaymentRequestPurposeProjection.class, obj)).collect(Collectors.toList());

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('paymentrequestpurposes','getallhousemaidspurpose')")
    @RequestMapping(value = "/getallhousemaidspurpose")
    public ResponseEntity<?> getAllHousemaidsPurpose(
            @RequestParam(name = "category", required = false) Long categoryID,
            @RequestParam(name = "search", required = false) String search) {

        SelectQuery<PaymentRequestPurpose> query = new SelectQuery<>(PaymentRequestPurpose.class);
        query.filterBy("forClient", "=", true);
        query.filterBy("active", "=", true);
        if (search != null && !search.isEmpty())
            query.filterBy("name", "like", "%"+search+"%");
        if (categoryID != null){
            query.filterBy("refundCategory", "like", picklistItemRepository.findOne(categoryID));
        }
        return new ResponseEntity<>(query.execute(), HttpStatus.OK);
//        if (search == null || search.isEmpty())
//            return new ResponseEntity<>(
//                    paymentRequestPurposeRepository.findByForHousemaidAndActive(true, true),
//                    HttpStatus.OK);
//        else
//            return new ResponseEntity<>(
//                    paymentRequestPurposeRepository.findByForHousemaidAndNameContainingAndActive(true, search, true),
//                    HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('paymentrequestpurposes','list')")
    @RequestMapping(value = "/csv", method = RequestMethod.GET)
    @JsonView(ViewScope.Normal.class)
    public void listCSV(HttpServletResponse response) {

        List<PaymentRequestPurpose> result = getRepository().findAll();
        
        InputStream is = null;
        try {
            String[] namesOrdared = new String[]{"category", "name", "threshold", "user"};
            is = generateCsv(result, PaymentRequestPurposeProjectionCsv.class, namesOrdared);
            createDownloadResponse(response, "PaymentRequestPurposes.csv", is);
        } catch (IOException ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }
}
