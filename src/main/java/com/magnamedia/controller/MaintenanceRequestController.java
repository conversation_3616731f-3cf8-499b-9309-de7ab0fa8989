package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.workflow.WorkflowController;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.MaintenanceRequest;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import com.magnamedia.module.type.MaintenanceRequestType;
import com.magnamedia.repository.ExpenseRepository;
import com.magnamedia.repository.MaintenanceRequestRepository;
import com.magnamedia.service.ExpenseNotificationService;
import com.magnamedia.workflow.service.MaintenanceRequestFlow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * Mohammad Nosairat (Feb 07, 2021)
 */
@RequestMapping("/maintenance-request")
@RestController
public class MaintenanceRequestController extends WorkflowController<MaintenanceRequest, MaintenanceRequestFlow> {

    @Autowired
    MaintenanceRequestRepository maintenanceRequestRepository;

    @Autowired
    ExpenseNotificationService expenseNotificationService;

    @Override
    protected SelectFilter filter(SelectFilter selectFilter, String s, List<String> list, Map<String, String> map) {
        return selectFilter;
    }

    @Override
    public BaseRepository<MaintenanceRequest> getRepository() {
        return maintenanceRequestRepository;
    }


    @Override
    protected ResponseEntity<?> createEntity(MaintenanceRequest entity) {
        if (entity.isQuotationReady())
            entity.setTaskName(MaintenanceRequestType.PURCHASE_AUDITOR_APPROVE_REQUEST.toString());
        else
            entity.setTaskName(MaintenanceRequestType.PURCHASE_MANAGER_GET_PRICE.toString());

        validateCreateMaintenance(entity);
        expenseNotificationService.expenseToDoCreatedEmail(entity.getEntityType(), entity.getTaskName());
        return super.createEntity(entity);
    }

    private void validateCreateMaintenance(MaintenanceRequest entity) {
        Expense expense = Setup.getRepository(ExpenseRepository.class).findOne(entity.getExpense().getId());
        if (expense.getBeneficiaryType() == null || !expense.getBeneficiaryType().equals(ExpenseBeneficiaryType.SUPPLIER))
            throw new RuntimeException("Expense type must be SUPPLIER");

        List<User> requesters = expense.getRequestors();
        User creator = CurrentRequest.getUser();
        if (requesters != null && !requesters.isEmpty() && requesters.stream().noneMatch(user -> user.getId().equals(creator.getId())))
            throw new RuntimeException("you are not authorized!");

    }
}
