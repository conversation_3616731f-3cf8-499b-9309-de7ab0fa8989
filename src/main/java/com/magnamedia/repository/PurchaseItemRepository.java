package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Item;
import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.module.type.PurchaseItemSupplierStatus;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * <PERSON> (Feb 01, 2021)
 */
@Repository
public interface PurchaseItemRepository extends BaseRepository<PurchaseItem> {
    List<PurchaseItem> findByPurchasingToDo(PurchasingToDo purchasingToDo);

    List<PurchaseItem> findByPurchasingToDoAndSupplierStatus(PurchasingToDo purchasingToDo, PurchaseItemSupplierStatus confirmed);

    List<PurchaseItem> findByPurchasingToDoAndSupplierStatusNotIn(PurchasingToDo purchasingToDo, List<PurchaseItemSupplierStatus> asList);

    List<PurchaseItem> findTop2ByItemOrderByCreationDateDesc(Item item);
}
