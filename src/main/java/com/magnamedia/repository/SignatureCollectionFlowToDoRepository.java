package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.SignatureCollectionFlowToDo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public interface SignatureCollectionFlowToDoRepository extends BaseRepository<SignatureCollectionFlowToDo> {

    SignatureCollectionFlowToDo findByContractAndIsActiveTrue(Contract contract);

    Page<SignatureCollectionFlowToDo>  findByLastMessageSentDateBetweenAndIsActiveTrue(Date startDate, Date endDate, Pageable pageable);

}
