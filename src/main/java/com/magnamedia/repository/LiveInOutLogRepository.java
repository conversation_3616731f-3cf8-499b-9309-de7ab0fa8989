/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.LiveInOutLog;
import com.magnamedia.module.type.LiveInOutLogReason;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */
@Repository
public interface LiveInOutLogRepository extends BaseRepository<LiveInOutLog> {
    
    public LiveInOutLog findFirstByHousemaidAndContractAndReasonInOrderByDateDesc(Housemaid housemaid,Contract contract, List<LiveInOutLogReason> reasons);
    public List<LiveInOutLog> findByHousemaidAndContractAndReasonInAndDateAfterAndDateBeforeOrderByDate(
            Housema<PERSON> housemaid,Contract contract, List<LiveInOutLogReason> reasons, Date start, Date end);

}
