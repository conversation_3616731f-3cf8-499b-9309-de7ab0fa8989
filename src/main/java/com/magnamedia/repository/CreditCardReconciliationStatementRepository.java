package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.CreditCardReconciliationStatement;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2/1/2021
 */
@Repository
public interface CreditCardReconciliationStatementRepository extends BaseRepository<CreditCardReconciliationStatement> {

    List<CreditCardReconciliationStatement> findByCreditCardAndStatusAndIsDeleted(Bucket creditCard, CreditCardReconciliationStatement.Status status, boolean deleted);
}
