package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.workflow.FlowProgressPeriod;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */

@Repository
public interface FlowProgressPeriodRepository extends BaseRepository<FlowProgressPeriod> {
    
    List<FlowProgressPeriod> findByFlowSubEventConfig(FlowSubEventConfig flowSubEventConfig);
    
    FlowProgressPeriod findFirstByFlowSubEventConfigAndTrialsAndReminders(
        FlowSubEventConfig flowSubEventConfig, int trials, int reminder);

    @Query("select p.periodInHours from FlowProgressPeriod p " +
            "where p.flowSubEventConfig.name = ?1 and p.trials = ?2 and p.reminders = ?3")
    Integer findPeriodInHoursByFlowSubEventConfig_NameAndTrialAndReminder(
            FlowSubEventConfig.FlowSubEventName name, int trial, int reminder);
}
