package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.workflow.CooNightReviewTodo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public interface CooNightReviewTodoRepository extends BaseRepository<CooNightReviewTodo> {

    @Query("SELECT t FROM CooNightReviewTodo t " +
            "WHERE t.type = ?1 and t.doneByCoo = false")
    Page<CooNightReviewTodo> findByTypeAndDoneByCooFalse(CooNightReviewTodo.CooNightReviewType type, Pageable pageable);

    @Query("SELECT t FROM CooNightReviewTodo t " +
            "WHERE t.type = ?1 and t.creationDate between ?2 AND ?3")
    Page<CooNightReviewTodo> findByTypeAndCreationDate(
            CooNightReviewTodo.CooNightReviewType type, Date first, Date second, Pageable pageable);
}