package com.magnamedia.repository;

import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Bucket;
import com.magnamedia.extra.BucketsInformation;
import com.magnamedia.module.type.BucketType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> <PERSON> at Nov 24, 2017
 */
@Repository
public interface BucketRepository extends BaseRepository<Bucket> {

    Boolean existsByCode(String code);

    Boolean existsByName(String name);

    Bucket findByCode(String code);

    Bucket findFirstByCode(String code);

    @Query("select count(b)>0 from Bucket b WHERE b.code =?2 AND b.id != ?1")
    Boolean existsBeforeByCode(Long id,
                               String code);

    @Query("select count(b)>0 from Bucket b WHERE b.name =?2 AND b.id != ?1")
    Boolean existsBeforeByName(Long id,
                               String name);

    //    List<HousemaidForgiveness> findByHousemaid(Housemaid housemaid);
    @Query("SELECT SUM (amount) FROM Transaction t WHERE t.toBucket = ?1 AND t.creationDate <= ?2 AND t.id < ?3")
    Double calculateIncomeBeforeDate(Bucket bucket,
                                     Date transactionDate,
                                     Long excludedId);

    @Query("SELECT SUM (amount) FROM Transaction t WHERE t.fromBucket = ?1 AND t.creationDate <= ?2 AND t.id < ?3")
    Double calculateOutcomeBeforeDate(Bucket bucket,
                                      Date transactionDate,
                                      Long excludedId);

    @Query("SELECT SUM (amount) FROM Transaction t WHERE t.toBucket = ?1 AND t.creationDate < ?2 ")
    Double calculateIncomeBeforeDate(Bucket bucket,
                                     Date transactionDate);

    @Query("SELECT SUM (amount) FROM Transaction t WHERE t.fromBucket = ?1 AND t.creationDate < ?2 ")
    Double calculateOutcomeBeforeDate(Bucket bucket,
                                      Date transactionDate);

    //    @Query("SELECT new com.magnamedia.extra.BucketsInformation(  BUCK, SUM(CASE WHEN TRANS.toBucket = BUCK THEN TRANS.amount ELSE 0 END), SUM(CASE WHEN TRANS2.fromBucket = BUCK THEN TRANS2.amount ELSE 0 END)) FROM Bucket BUCK LEFT JOIN BUCK.incomeTransactions as TRANS LEFT JOIN BUCK.outcomeTransactions as TRANS2 GROUP BY BUCK.code")
//        @Query("SELECT new com.magnamedia.extra.BucketsInformation(  BUCK.id, SUM(CASE WHEN TRANS.toBucket = BUCK THEN TRANS.amount ELSE 0 END)) FROM Bucket BUCK LEFT JOIN BUCK.incomeTransactions as TRANS  GROUP BY BUCK.id")
//    @Query("SELECT new com.magnamedia.extra.BucketsInformation(  BUCK.id, SUM(CASE WHEN TRANS.toBucket = BUCK THEN TRANS.amount ELSE 0 END) , SUM(CASE WHEN TRANSOUT.fromBucket = BUCK THEN TRANSOUT.amount ELSE 0 END)) FROM Bucket BUCK LEFT JOIN BUCK.incomeTransactions as TRANS LEFT JOIN BUCK.outcomeTransactions as TRANSOUT  WHERE BUCK.id = TRANS.toBucket OR BUCK.id = TRANSOUT.fromBucket GROUP BY BUCK.id")
//        @Query("SELECT new com.magnamedia.extra.BucketsInformation(  BUCK.id, SUM(CASE WHEN TRANS.toBucket = BUCK THEN TRANS.amount ELSE 0 END) , SUM(CASE WHEN TRANS.fromBucket = BUCK THEN TRANS.amount ELSE 0 END)) FROM Bucket BUCK LEFT JOIN BUCK.outcomeTransactions as TRANS ON BUCK.id = TRANS.toBucket OR BUCK.id = TRANS.fromBucket GROUP BY BUCK.id")
    @Query(nativeQuery = true, value = "SELECT CODE AS CODE, BUCK.ID AS ID, SUM(CASE WHEN TRANS.TO_BUCKET_ID = BUCK.ID THEN AMOUNT ELSE 0 END) AS TOTALSUM, SUM(CASE WHEN TRANS.FROM_BUCKET_ID = BUCK.ID THEN AMOUNT ELSE 0 END) AS TOTALMINUS FROM BUCKETS AS BUCK LEFT JOIN TRANSACTIONS AS TRANS ON BUCK.ID = TRANS.TO_BUCKET_ID OR BUCK.ID = TRANS.FROM_BUCKET_ID GROUP BY BUCK.ID", countQuery = "SELECT COUNT(0) FROM BUCKETS")
    public Page<Object> getBucketsBalanceInformation(Pageable pageable);

    @Query(nativeQuery = true, 
            value = "SELECT CODE AS CODE, BUCK.ID AS ID, " +
                        "(select COALESCE(SUM(t.amount),0) from TRANSACTIONS t where t.TO_BUCKET_ID = BUCK.id) AS TOTALSUM, " +
                        "(select COALESCE(SUM(t.amount),0) from TRANSACTIONS t where t.FROM_BUCKET_ID  = BUCK.id) AS TOTALMINUS " +
                    "FROM BUCKETS AS BUCK " +
                    "WHERE BUCK.ID IN ?1 " +
                    "GROUP BY BUCK.ID")
    List<BucketsInformation> getBucketsBalanceInformation2(List<Long> ids);

    // Testing
    @Query(value = "select b from Bucket b where b.holderEmail is not null and b.holderEmail <>''")
    Page<Bucket> findBucketsWithEmail(Pageable pageable);

    List<Bucket> findByHolderAndBucketType(User holder, BucketType bucketType);

    Bucket findFirstByHolderAndBucketType(User holder, BucketType bucketType);

    boolean existsByHolderAndBucketType(User holder, BucketType bucketType);

    List<Bucket> findByHolderAndBucketTypeAndIdNotIn(User holder, BucketType bucketType, List<Long> ids);

    List<Bucket> findByBucketType(BucketType bucketType);

    Bucket findFirstByBucketType(BucketType bucketType);
    Bucket findFirstByHolderEmail(String holderEmail);

    Page<Bucket> findByBucketTypeInAndIsActiveAndAutoReplenishment(List<BucketType> bucketTypes, Boolean isActive, Boolean autoReplenishment, Pageable pageable);

    @Query("select b.code, b.id, b.name from Bucket b " +
            "where b.code in (?1)")
    List<Object[]> findIdAndNameByCode(List<String> codes);

    @Query(value = "select new map(b as bucket, " +
                        "ifNull((select sum(t.amount) from Transaction t where t.toBucket = b), 0) - " +
                            "ifNull((select sum(t.amount) FROM Transaction t where t.fromBucket = b), 0) - " +
                            "b.authorizedBalance as correctBalance) " +
                    "from Bucket b " +
                    "where b.id > ?1",
            countQuery = "select count(b.id) from Bucket b where b.id > ?1")
    Page<Map<String,Object>> findAllBucketsWithCalculatesCorrectBalance(Long id, Pageable pageable);

    @Query(value = "select ifNull((select sum(t.amount) from Transaction t where t.toBucket = b), 0) - " +
                            "ifNull((select sum(t.amount) FROM Transaction t where t.fromBucket = b), 0) - " +
                            "b.authorizedBalance as correctBalance " +
                    "from Bucket b " +
                    "where b.id = ?1")
    Double findAllBucketsWithCalculatesCorrectBalance(Long id);
}
