package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DDMsgConfig;
import com.magnamedia.module.type.DDMsgConfigType;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */

@Repository
public interface DDMsgConfigRepository extends BaseRepository<DDMsgConfig> {

    DDMsgConfig findFirstByMsgType(DDMsgConfigType msgType);
}
