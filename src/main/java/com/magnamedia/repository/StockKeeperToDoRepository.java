package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.StockKeeperToDo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <PERSON> (Feb 09, 2021)
 */
@Repository
public interface StockKeeperToDoRepository extends BaseRepository<StockKeeperToDo> {
    StockKeeperToDo findTop1ByExpenseRequestTodoOrderByCreationDateDesc(ExpenseRequestTodo expenseRequestTodo);

    List<StockKeeperToDo> findByClosedFalse();
}
