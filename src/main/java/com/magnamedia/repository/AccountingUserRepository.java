package com.magnamedia.repository;

import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AccountingUserRepository extends BaseRepository<User> {

    @Query("select u from User u " +
            "where upper(u.email) = upper(?1) and u.activated = true")
    List<User> findLinkedUserByEmail(String email);
}