package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AccountingLink;
import org.springframework.stereotype.Repository;


@Repository
public interface AccountingLinkRepository extends BaseRepository<AccountingLink> {

//    @Query("select count(a.id) > 0 " +
//            "from AccountingLink a " +
//            "where a.uuid = ?1 and a.status = 'EXPIRED'")
//    boolean isLinkExpired(String uuid);
//
//    List<AccountingLink> findByRelatedEntityIdAndRelatedEntityTypeAndTypeAndStatus(
//            Long relatedEntityId, String relatedEntityType,
//            AccountingLink.AccountingLinkType type, AccountingLink.AccountingLinkStatus status);

    AccountingLink findFirstByRelatedEntityIdAndRelatedEntityTypeAndTypeAndStatusAndOriginalLink(
            Long relatedEntityId, String relatedEntityType, AccountingLink.AccountingLinkType type,
            AccountingLink.AccountingLinkStatus status, String originalLink);

    AccountingLink findTopByContractIdAndTypeOrderByCreationDateDesc(Long contractId, AccountingLink.AccountingLinkType type);
}