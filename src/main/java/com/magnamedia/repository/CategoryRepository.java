package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Category;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <PERSON> (Jan 25, 2021)
 */
@Repository
public interface CategoryRepository extends BaseRepository<Category> {
    Category findByCategoryId(String categoryId);
    Category findFirstByName(String name);

    List<Category> findByOrderCycle(PicklistItem orderCycle);
    List<Category> findByOrderCycleAndInActiveCycleTrue(PicklistItem orderCycle);
}
