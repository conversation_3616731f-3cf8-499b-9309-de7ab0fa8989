/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.repository;

import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.module.type.OfficeStaffStatus;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@Repository
public interface OfficeStaffRepository extends BaseRepository<OfficeStaff> {
    
    @Query("SELECT o from OfficeStaff o left outer join o.manager left outer join o.team where o.status=com.magnamedia.module.type.OfficeStaffStatus.ACTIVE" +
            " order by o.manager.name asc, o.team.name asc")
    List<OfficeStaff> findActiveOfficeStaffOrdered();


    @Query("SELECT o from OfficeStaff o where o.status=com.magnamedia.module.type.OfficeStaffStatus.TERMINATED" +
            " order by o.terminationDate desc")
    List<OfficeStaff> findTerminatedOfficeStaffOrdered();
    
    List<OfficeStaff> findByStatusOrderByTerminationDate(OfficeStaffStatus status);
    
    List<OfficeStaff> findByStatusOrderByManager_Name(OfficeStaffStatus status);
    
    List<OfficeStaff> findByStatusAndTerminationDateAfterOrderByTerminationDate(OfficeStaffStatus status, Date start);

    OfficeStaff findFirstByPhoneNumber(String phoneNumber);
    @Query("SELECT u from User u where u.relatedEntityId = ?1 and u.relatedEntityType = 'OfficeStaff'")
    List<User> getUsers(Long officeStaffId);

    @Query("select distinct o from OfficeStaff o " +
            "join o.visaNewRequest v " +
            "inner join Attachment a " +
            "on a.ownerId = v.id and a.ownerType = 'NewRequest' and a.tag = 'insurancePolicy' " +
            "where o.employeeType = 'DUBAI_STAFF_EXPAT' and (o.status <> 'TERMINATED' or o.terminationDate > ?1) " +
            "and v.passportId not in (?2)")
    List<OfficeStaff> getActiveStaffsInERPButNotInFile(Date d, List<String> passports);

    @Query("select distinct o from OfficeStaff o " +
            "join o.visaNewRequest v " +
            "where v.passportId in ?1 and o.status = 'TERMINATED' and o.terminationDate <= ?2 and " +
            "not exists (select 1 from OfficeStaff o1 " +
                        "join o1.visaNewRequest v1 " +
                        "where o1.id <> o.id and v.passportId = v1.passportId and " +
                            "(o1.status <> 'TERMINATED' or o1.terminationDate > ?2 ))")
    List<OfficeStaff> getActiveStaffsInFileButNotInERP(List<String> passports, Date d);

    @Query("select distinct o from OfficeStaff o where upper(o.email) = upper(?1)")
    List<OfficeStaff> findOfficeStaffByLinkedUserEmail(String email);

    @Query("select o from OfficeStaff o " +
            "where o.status = 'ACTIVE' and o.email is not null and o.email <> '' and " +
                "o.employeeManager.id in ?1 and o.id not in ?2")
    List<OfficeStaff> findEmployeesByManager(Set<Long> ids1, Set<Long> ids2);
}
