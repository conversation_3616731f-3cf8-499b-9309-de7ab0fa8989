package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DirectDebitSignature;
import com.magnamedia.extra.DDSignatureTempDto;
import com.magnamedia.module.type.DirectDebitSignatureStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;


@Repository
public interface DirectDebitSignatureRepository extends BaseRepository<DirectDebitSignature> {

    @Query("select s from DirectDebitSignature s " +
            "where s.contractPaymentTerm.contract.client = ?1")
    List<DirectDebitSignature> findOldSignaturesByClient(Client client);

    @Query("select distinct sign from DirectDebitFile ddf " +
            "inner join ddf.directDebitSignature sign " +
            "inner join ddf.directDebit ddm " +
            "where ddm.contractPaymentTerm.contract = ?1 and " +
                "(sign.eid = ?2 or sign.eid is null) and sign.signatureStatus <> 'REJECTED' " +
            "order by case sign.signatureStatus " +
                "when 'APPROVED' then 1 " +
                "when 'UNUSED' then 2 " +
                "when 'UNDER_PROCESS' then 3 " +
                "else 4 end, sign.id")
    List<DirectDebitSignature> findSignaturesByContractWithDdf(Contract contract, String eid);

    @Query("select distinct sign from DirectDebitFile ddf " +
            "inner join ddf.directDebitSignature sign " +
            "inner join ddf.directDebit ddm " +
            "where ddm.contractPaymentTerm.contract.client = ?1 and " +
                "(sign.eid = ?2 or sign.eid is null) and sign.signatureStatus <> 'REJECTED' and sign.disable = ?3 " +
            "order by case sign.signatureStatus " +
                "when 'APPROVED' then 1 " +
                "when 'UNUSED' then 2 " +
                "when 'UNDER_PROCESS' then 3 " +
                "else 4 end, sign.id")
    List<DirectDebitSignature> findSignaturesByClientWithDdf(
            Client client, String eid, boolean disable);

    @Query("select distinct sign from DirectDebitSignature sign " +
            "where sign.contractPaymentTerm.contract.client = ?1 and " +
                "(sign.eid = ?2 or sign.eid is null) and sign.signatureStatus <> 'REJECTED' and sign.disable = ?3 and " +
                "not EXISTS (select 1 from DirectDebitFile ddf where ddf.directDebitSignature  = sign) " +
            "order by case sign.signatureStatus " +
                "when 'APPROVED' then 1 " +
                "when 'UNUSED' then 2 " +
                "when 'UNDER_PROCESS' then 3 " +
                "else 4 end, sign.id")
    List<DirectDebitSignature> findSignaturesByClientWithoutDdf(
            Client client, String eid, boolean disable);

    @Query("select count(sign.id) > 0 " +
            "from DirectDebitFile ddf " +
            "inner join ddf.directDebitSignature sign " +
            "inner join ddf.directDebit ddm " +
            "where ddm.contractPaymentTerm.contract.client = :client and " +
                "(:status is null or sign.signatureStatus = :status) and " +
                "(sign.eid = :eid or sign.eid is null) and sign.signatureStatus <> 'REJECTED' and sign.disable = false ")
    boolean existsSignaturesByClientWithDdf(
            @Param("client") Client client, @Param("eid") String eid, @Param("status") DirectDebitSignatureStatus s);

    @Query("select count(sign.id) > 0 " +
            "from DirectDebitSignature sign " +
            "where sign.contractPaymentTerm.contract.client = :client and " +
                "(sign.eid = :eid or sign.eid is null) and sign.signatureStatus <> 'REJECTED' and sign.disable = false and " +
                "(:status is null or sign.signatureStatus = :status) and " +
                "not EXISTS (select 1 from DirectDebitFile ddf where ddf.directDebitSignature  = sign) ")
    boolean existSignaturesByClientWithoutDdf(
            @Param("client") Client client, @Param("eid") String eid, @Param("status") DirectDebitSignatureStatus s);

    @Query("select distinct sign from DirectDebitFile ddf " +
            "inner join ddf.directDebitSignature sign " +
            "inner join ddf.directDebit ddm " +
            "where ddm.contractPaymentTerm.contract.client = ?1 and " +
                "(sign.eid = ?2 or sign.eid is null) and sign.signatureStatus <> 'REJECTED' " +
            "order by case sign.signatureStatus " +
                "when 'APPROVED' then 1 " +
                "when 'UNUSED' then 2 " +
                "when 'UNDER_PROCESS' then 3 " +
                "else 4 end, sign.id")
    List<DirectDebitSignature> findSignaturesByClientWithDdfConsiderDisabled(Client client, String eid);

    @Query("select distinct sign from DirectDebitSignature sign " +
            "where sign.contractPaymentTerm.contract.client = ?1 and " +
                "(sign.eid = ?2 or sign.eid is null) and sign.signatureStatus <> 'REJECTED' and " +
                "not EXISTS (select 1 from DirectDebitFile ddf where ddf.directDebitSignature  = sign) " +
            "order by case sign.signatureStatus " +
                "when 'APPROVED' then 1 " +
                "when 'UNUSED' then 2 " +
                "when 'UNDER_PROCESS' then 3 " +
                "else 4 end, sign.id")
    List<DirectDebitSignature> findSignaturesByClientWithoutDdfConsiderDisabled(Client client, String eid);

    // ACC-3447
    @Query("select distinct sign from DirectDebitFile ddf " +
            "inner join ddf.directDebitSignature sign " +
            "inner join ddf.directDebit ddm " +
            "where ddm.contractPaymentTerm.contract.client = ?1 and " +
                "sign.eid = ?2 and sign.signatureStatus = 'REJECTED'")
    List<DirectDebitSignature> findRejectedSignaturesByClient(Client client, String eid);

    //ACC-3989 dataMigrationACC3989
    @Query(nativeQuery = true, 
            value = "Select t.CPT_ID as cptId, t.EID as eid, t.DIRECT_DEBIT_FILE_ID as directDebitFileId, " +
                        "t.NEW_ATTACHMENT_ID as newAttachmentId, " +
                    "Case when DDF.STATUS = 'REJECTED' AND DDF.REJECT_CATEGORY = 'Signature' Then 'REJECTED' " +
                        "when DDF.STATUS = 'REJECTED' AND (DDF.REJECT_CATEGORY <> 'Signature' OR DDF.REJECT_CATEGORY is null) Then 'NOT_COMPLETED' " +
                        "WHEN DDF.STATUS = 'APPROVED' THEN 'APPROVED' " +
                        "WHEN DDF.STATUS = 'SENT' THEN 'SENT' " +
                        "WHEN DDF.STATUS = 'NOT_SENT' THEN 'NOT_SENT' " +
                        "WHEN DDF.STATUS = 'NOT_COMPLETED' THEN 'NOT_COMPLETED' end as directDebitFileStatus " +
                    "FROM DDSIGNATURETEMPS t " +
                    "INNER JOIN DIRECTDEBITFILES DDF ON t.DIRECT_DEBIT_FILE_ID = DDF.id " +
                    "where t.CPT_ID = ?1 " +
                    "ORDER BY t.NEW_ATTACHMENT_ID , CASE directDebitFileStatus " +
                        "WHEN 'REJECTED' THEN 1 " +
                        "WHEN 'APPROVED' THEN 2 " +
                        "WHEN 'SENT' THEN 3 " +
                        "WHEN 'NOT_SENT' THEN 4 " +
                        "WHEN 'NOT_COMPLETED' THEN 5 " +
                        "ELSE 6 END" )
    List<DDSignatureTempDto> getNotCorrectedCPTSignAttachments(Long cptID);

    @Query("SELECT t.cptId FROM DDSignatureTemp t " +
            "where not exists (select 1 from DirectDebitSignature dds where dds.contractPaymentTerm.id = t.cptId) " + 
            "group by t.cptId " +
            "order by t.cptId")
    Page<Long> getNotCorrectedCptSecondStep(Pageable pageable);

    Boolean existsByContractPaymentTerm_Id(Long cptId);

    @Query(nativeQuery = true,
        value = "Select t.CPT_ID as cptId, t.EID as eid, t.DIRECT_DEBIT_FILE_ID as directDebitFileId, " +
                    "t.NEW_ATTACHMENT_ID as newAttachmentId, " +
                "Case when DDF.STATUS = 'REJECTED' AND DDF.REJECT_CATEGORY = 'Signature' Then 'REJECTED' " +
                    "when DDF.STATUS = 'REJECTED' AND (DDF.REJECT_CATEGORY <> 'Signature' OR DDF.REJECT_CATEGORY is null) Then 'NOT_COMPLETED' " +
                    "WHEN DDF.STATUS = 'APPROVED' THEN 'APPROVED' " +
                    "WHEN DDF.STATUS = 'SENT' THEN 'SENT' " +
                    "WHEN DDF.STATUS = 'NOT_SENT' THEN 'NOT_SENT' " +
                    "WHEN DDF.STATUS = 'NOT_COMPLETED' THEN 'NOT_COMPLETED' end as directDebitFileStatus " +
                "FROM DDSIGNATURETEMPS t " +
                "INNER JOIN DIRECTDEBITFILES DDF ON t.DIRECT_DEBIT_FILE_ID = DDF.id " +
                "where t.DIRECT_DEBIT_FILE_ID in (?1) " +
                "ORDER BY t.NEW_ATTACHMENT_ID , CASE directDebitFileStatus " +
                    "WHEN 'REJECTED' THEN 1 " +
                    "WHEN 'APPROVED' THEN 2 " +
                    "WHEN 'SENT' THEN 3 " +
                    "WHEN 'NOT_SENT' THEN 4 " +
                    "WHEN 'NOT_COMPLETED' THEN 5 " +
                "ELSE 6 END" )
    List<DDSignatureTempDto> getNotCorrectedCPTSignAttachmentsRefresh(List<String> ddfIds);

    @Query("SELECT distinct t.directDebitFileId FROM DDSignatureTemp t " +
            "where exists (select 1 from DirectDebitFile ddf where ddf.id = t.directDebitFileId " +
                "and ddf.directDebitSignature is null) " +
            "order by t.directDebitFileId")
    Page<Long> getNotCorrectedCptSecondStepRefresh(Pageable pageable);

    // ACC-5156
    @Query("select distinct sign from DirectDebitFile ddf " +
        "inner join ddf.directDebitSignature sign " +
        "inner join ddf.directDebit ddm " +
        "where ddm.contractPaymentTerm.contract.client = ?1 and sign.eid = ?2 and " +
            "sign.signatureStatus = 'REJECTED' and sign.approvedOnce = true")
    List<DirectDebitSignature> findRejectedSignaturesByClientAndApprovedOnce(Client client, String eid);

    @Query(nativeQuery = true,
        value = "select DISTINCT dds.ID from DIRECTDEBITSIGNATURES dds " +
                "inner join DIRECTDEBITFILES ddf on ddf.DIRECT_DEBIT_SIGNATURE_ID = dds.ID " +
                "where EXISTS (select 1 from DIRECTDEBITFILES_REVISIONS ddf_r WHERE ddf.ID = ddf_r.ID and ddf_r.STATUS = 'APPROVED') " +
                "order by dds.ID")
    Page<BigInteger> findByDDFApproved(Pageable pageable);

    List<DirectDebitSignature> findByContractPaymentTermAndSignatureStatusAndEidNotNull(
            ContractPaymentTerm cpt, DirectDebitSignatureStatus s);

    @Query("select coalesce(max(d.setOrder), 0) from DirectDebitSignature d " +
            "where d.contractPaymentTerm.contract = ?1 ")
    Integer getMaxSetOrderByContract(Contract c);
}