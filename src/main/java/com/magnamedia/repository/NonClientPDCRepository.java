package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.NonClientPDC;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Dec 28, 2017
 */
@Repository
public interface NonClientPDCRepository extends BaseRepository<NonClientPDC> {

    @Query("SELECT p FROM NonClientPDC p "
            + "WHERE p.chequeDueDate between :first AND :second "
            + "ORDER BY p.chequeDueDate")
    List<NonClientPDC> findByDueDateBetween(@Param("first") Date first, @Param("second") Date second);

    @Query("SELECT p FROM NonClientPDC p "
            + "WHERE contractEndDate between :first AND :second")
    List<NonClientPDC> findByEndoOfContractBetween(@Param("first") Date first, @Param("second") Date second);

    @Query("SELECT new map(p.id as id, p.propertyDiscription as propertyDescription, p.leaseCompanyName as leaseCompanyName, " +
            "p.chequeIssuerName as chequeIssuerName, p.chequeDueDate as chequeDueDate, p.amount as amount, " +
            "p.contractEndDate as contractEndDate, b as bankIssuerBucket)" +
            " FROM NonClientPDC p left join Bucket b on p.bankIssuerBucket = b "
            + "WHERE p.chequeDueDate between ?1 AND ?2 "
            + "ORDER BY p.chequeDueDate")
    Page<Map<String, Object>> findByDueDateBetween(Date first, Date second, Pageable pageable);

    @Query("SELECT new map(p.id as id, p.propertyDiscription as propertyDescription, p.leaseCompanyName as leaseCompanyName, " +
            "p.chequeIssuerName as chequeIssuerName, p.chequeDueDate as chequeDueDate, p.amount as amount, " +
            "p.contractEndDate as contractEndDate, b as bankIssuerBucket)" +
            " FROM NonClientPDC p left join Bucket b on p.bankIssuerBucket = b "
            + "WHERE p.chequeDueDate between ?1 AND ?2 AND p.doneByCoo = false "
            + "ORDER BY p.chequeDueDate")
    Page<Map<String, Object>> findByDueDateBetweenAndDoneByCooFalse(Date first, Date second, Pageable pageable);
}