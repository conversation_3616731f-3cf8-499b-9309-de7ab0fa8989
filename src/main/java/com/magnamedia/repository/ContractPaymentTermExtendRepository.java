/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.ContractPaymentTermExtend;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */

@Repository
public interface ContractPaymentTermExtendRepository extends BaseRepository<ContractPaymentTermExtend> {

    @Query("Select coalesce(Sum(c.paymentDuration),0) from ContractPaymentTermExtend c where c.contractPaymentTerm = :term ")
    Integer getExtendDurationByContractTerm(@Param("term") ContractPaymentTerm term);
}
