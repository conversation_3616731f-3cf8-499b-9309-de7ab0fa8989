package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AmwalStatementRecord;
import com.magnamedia.entity.VisaStatement;
import com.magnamedia.entity.VisaStatementTransaction;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AmwalStatementRecordRepository extends BaseRepository<AmwalStatementRecord> {

    public AmwalStatementRecord findTopByTransaction(VisaStatementTransaction transaction);

    public List<AmwalStatementRecord> findByStatement(VisaStatement statement);
}
