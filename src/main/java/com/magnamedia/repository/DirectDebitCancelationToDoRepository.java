package com.magnamedia.repository;

import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.module.type.DirectDebitStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Nov 20, 2019
 *         Jirra ACC-1134
 * 
 */
@Repository
public interface DirectDebitCancelationToDoRepository extends WorkflowRepository<DirectDebitCancelationToDo> {

    Boolean existsByDirectDebitFileAndCompletedFalseAndStoppedFalse(DirectDebitFile directDebitFile);
    
    List<DirectDebitCancelationToDo> findByDirectDebitFileAndCompletedFalseAndStoppedFalse(DirectDebitFile directDebitFile);

    DirectDebitCancelationToDo findFirstByDirectDebitFileAndCompletedTrueAndStoppedFalseOrderByCreationDateDesc(DirectDebitFile directDebitFile);

    DirectDebitCancelationToDo findFirstByDirectDebitFileOrderByCreationDateDesc(DirectDebitFile directDebitFile);

    List<DirectDebitCancelationToDo> findByDirectDebitFile_DirectDebitAndCompletedFalseAndStoppedFalse(DirectDebit directDebit);

    List<DirectDebitCancelationToDo> findByDirectDebitFile_DirectDebitAndCompletedFalseAndStoppedFalseAndReason(DirectDebit directDebit, DirectDebitCancellationToDoReason reason);

    boolean existsByDirectDebitFile_DirectDebitAndCompletedFalseAndStoppedFalse(DirectDebit directDebit);

    List<DirectDebitCancelationToDo> findByHidden(Boolean hidden);

    List<DirectDebitCancelationToDo> findByIdIn(List<Long> ids);

    @Query("select ddCToDo from DirectDebitCancelationToDo ddCToDo where " +
            "ddCToDo.directDebit.contractPaymentTerm = :cpt and " +
            "ddCToDo.hidden = :hidden and ddCToDo.reason in :reasons")
    List<DirectDebitCancelationToDo> findByCPTAndHiddenAndReasonIn(@Param("cpt") ContractPaymentTerm cpt,
                                                                   @Param("hidden") Boolean hidden,
                                                                   @Param("reasons") List<DirectDebitCancellationToDoReason> reason);

    @Query("select ddCToDo from DirectDebitCancelationToDo ddCToDo where " +
            "(ddCToDo.directDebit.status = :ddStatus or ddCToDo.directDebit.MStatus = :ddStatus) and " +
            "ddCToDo.hidden = :hidden and ddCToDo.reason in :reasons")
    List<DirectDebitCancelationToDo> findByHiddenAndReasonInAndDirectDebit_Status(@Param("hidden") Boolean hidden,
                                                                                  @Param("reasons") List<DirectDebitCancellationToDoReason> reason,
                                                                                  @Param("ddStatus") DirectDebitStatus status);

    @Query("select ddCToDo from DirectDebitCancelationToDo ddCToDo where " +
            "ddCToDo.hidden = :hidden and ((ddCToDo.reason not in :reasons) or (ddCToDo.reason is null)) and ddCToDo.id not in :ids")
    Page<DirectDebitCancelationToDo> findByHiddenAndReasonNotInAndIdNotIn(@Param("hidden") Boolean hidden,
                                                                          @Param("reasons") List<DirectDebitCancellationToDoReason> reason,
                                                                          @Param("ids") List<Long> ids,
                                                                          Pageable pageable);

    @Query("select d from DirectDebitCancelationToDo d " +
            "where d.directDebit.contractPaymentTerm.contract = ?1 and d.stopped = false and d.completed = false and " +
                "d.reason = 'CONTRACT_CANCELLATION' and d.creationDate >= ?2")
    List<DirectDebitCancelationToDo> findByContractAndCreationDate(Contract c, Date d);
}
