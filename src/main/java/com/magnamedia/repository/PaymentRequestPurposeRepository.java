package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;

import java.util.List;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Aug 24, 2019
 * ACC-837
 */
@Repository
public interface PaymentRequestPurposeRepository extends BaseRepository<PaymentRequestPurpose> {

    List<PaymentRequestPurpose> findByForClientAndActive(boolean forClient, boolean active);

    List<PaymentRequestPurpose> findByForClientAndActiveAndIdIn(Boolean forClient,Boolean active,List<Long> ids);

    List<PaymentRequestPurpose> findByForHousemaidAndActive(boolean forHousemaid, boolean active);

    List<PaymentRequestPurpose> findByForClientAndNameContainingAndActive(
            boolean forClient, String name, boolean active);

    List<PaymentRequestPurpose> findByForClientAndNameContainingAndActiveAndIdIn
            (Boolean forClient,String name,Boolean active,List<Long> ids);

    // Jirra ACC-1241
    List<PaymentRequestPurpose> findByForClientAndNameEquals(
            boolean forClient, String name);

    // Jirra ACC-1721
    PaymentRequestPurpose findFirstByForClientAndNameEquals(
            boolean forClient, String name);

    List<PaymentRequestPurpose> findByForHousemaidAndNameContainingAndActive(
            boolean forHousemaid, String name, boolean active);

    List<PaymentRequestPurpose> findByForHousemaidAndNameContaining(
            boolean forHousemaid, String name);

    PaymentRequestPurpose findByName(String name);
}
