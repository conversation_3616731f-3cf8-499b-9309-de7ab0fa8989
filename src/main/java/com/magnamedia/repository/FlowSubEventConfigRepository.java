package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */

@Repository
public interface FlowSubEventConfigRepository extends BaseRepository<FlowSubEventConfig> {
    
    List<FlowSubEventConfig> findByFlowEventConfig(FlowEventConfig flowEventConfig);

    FlowSubEventConfig findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName name, FlowEventConfig flowEventConfig);

    FlowSubEventConfig findByNameAndFlowEventConfig_Name(FlowSubEventConfig.FlowSubEventName name, FlowEventConfig.FlowEventName flowEventName);

    @Query("select f from FlowSubEventConfig f " +
            "where f.flowEventConfig.name = ?1 and f.name in ?2 ")
    List<FlowSubEventConfig> findAllSubFlowsByFlowEventNameAndFlowSubEventNamesIn(FlowEventConfig.FlowEventName flowName, List<FlowSubEventConfig.FlowSubEventName> recurringFailureFlows);
}