package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.MaidInAccommodationStatistics;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <PERSON> (Mar 14, 2021)
 */
@Repository
public interface MaidInAccommodationStatisticsRepository extends BaseRepository<MaidInAccommodationStatistics> {
    List<MaidInAccommodationStatistics> findByCreationDateGreaterThanEqualAndCreationDateLessThanEqual(Date begin, Date end);
}
