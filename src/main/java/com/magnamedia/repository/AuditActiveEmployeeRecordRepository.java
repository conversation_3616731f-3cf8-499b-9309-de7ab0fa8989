package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AuditActiveEmployeeRecord;
import com.magnamedia.entity.AuditActiveEmployeesFile;
import com.magnamedia.extra.AuditActiveEmployeeRecordType;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AuditActiveEmployeeRecordRepository extends BaseRepository<AuditActiveEmployeeRecord> {

    List<AuditActiveEmployeeRecord> findByFile(AuditActiveEmployeesFile entity);

    List<AuditActiveEmployeeRecord> findByFileAndType(AuditActiveEmployeesFile entity, AuditActiveEmployeeRecordType type);
}