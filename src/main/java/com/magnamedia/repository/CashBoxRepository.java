package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.CashBox;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <PERSON> (Jan 27, 2021)
 */
@Repository
public interface CashBoxRepository extends BaseRepository<CashBox> {
    List<CashBox> findAllByCreationDateGreaterThanOrderByCreationDateDesc(Date date);

    CashBox findTop1ByBucketOrderByLastCloseDateDesc(Bucket bucket);
}
