package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.ClientDocument;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created at Nov 4, 2017
 * 
 */
@Repository
public interface ClientDocumentRepository extends BaseRepository<ClientDocument> {
    List<ClientDocument> findByClientAndType(Client id, PicklistItem type);

    // ACC-1435
    Boolean existsByClientAndType(Client id, PicklistItem type);
}
