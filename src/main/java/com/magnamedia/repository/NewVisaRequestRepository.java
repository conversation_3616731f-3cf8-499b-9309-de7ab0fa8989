package com.magnamedia.repository;

import com.magnamedia.entity.NewRequest;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Sep 16, 2017
 */
@Repository
public interface NewVisaRequestRepository extends VisaRequestRepository<NewRequest> {

    @Query(value = "select v from NewRequest v " +
            "where regexp_replace(v.newEidNumber, '[^0-9]', '') = regexp_replace(?1, '[^0-9]', '') " +
            "order by v.creationDate desc")
    List<NewRequest> findVisaNewRequestByNewEidNumberOrderByCreationDate(String eidNumber);
}
